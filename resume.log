This is pdfTeX, Version 3.141592653-2.6-1.40.27 (MiKTeX 25.4) (preloaded format=pdflatex 2025.8.30)  30 AUG 2025 14:19
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**d:/workspace/CV/resume.tex
(d:/workspace/CV/resume.tex
LaTeX2e <2025-06-01> patch level 1
L3 programming layer <2025-07-20>
(russell.cls
Document Class: russell 2017/02/05 v1.6.1 russell Curriculum Vitae Class
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\article.cls
Document Class: article 2025/01/22 v1.4n Standard LaTeX document class
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\size11.clo
File: size11.clo 2025/01/22 v1.4n Standard LaTeX file (size option)
)
\c@part=\count275
\c@section=\count276
\c@subsection=\count277
\c@subsubsection=\count278
\c@paragraph=\count279
\c@subparagraph=\count280
\c@figure=\count281
\c@table=\count282
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen148
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tools\array.sty
Package: array 2025/06/08 v2.6j Tabular extension package (FMi)
\col@sep=\dimen149
\ar@mcellbox=\box53
\extrarowheight=\dimen150
\NC@list=\toks17
\extratabsurround=\skip51
\backup@length=\skip52
\ar@cellbox=\box54
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/enumitem\enumitem.sty
Package: enumitem 2025/02/06 v3.11 Customized lists
\enitkv@toks@=\toks18
\labelindent=\skip53
\enit@outerparindent=\dimen151
\enit@toks=\toks19
\enit@inbox=\box55
\enit@count@id=\count283
\enitdp@description=\count284
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/ragged2e\ragged2e.sty
Package: ragged2e 2023/06/22 v3.6 ragged2e Package
\CenteringLeftskip=\skip54
\RaggedLeftLeftskip=\skip55
\RaggedRightLeftskip=\skip56
\CenteringRightskip=\skip57
\RaggedLeftRightskip=\skip58
\RaggedRightRightskip=\skip59
\CenteringParfillskip=\skip60
\RaggedLeftParfillskip=\skip61
\RaggedRightParfillskip=\skip62
\JustifyingParfillskip=\skip63
\CenteringParindent=\skip64
\RaggedLeftParindent=\skip65
\RaggedRightParindent=\skip66
\JustifyingParindent=\skip67
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks20
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
))
\Gm@cnth=\count285
\Gm@cntv=\count286
\c@Gm@tempcnt=\count287
\Gm@bindingoffset=\dimen152
\Gm@wd@mp=\dimen153
\Gm@odd@mp=\dimen154
\Gm@even@mp=\dimen155
\Gm@layoutwidth=\dimen156
\Gm@layoutheight=\dimen157
\Gm@layouthoffset=\dimen158
\Gm@layoutvoffset=\dimen159
\Gm@dimlist=\toks21
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.cfg)) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/fancyhdr\fancyhdr.sty
Package: fancyhdr 2025/02/07 v5.2 Extensive control of page headers and footers
\f@nch@headwidth=\skip68
\f@nch@offset@elh=\skip69
\f@nch@offset@erh=\skip70
\f@nch@offset@olh=\skip71
\f@nch@offset@orh=\skip72
\f@nch@offset@elf=\skip73
\f@nch@offset@erf=\skip74
\f@nch@offset@olf=\skip75
\f@nch@offset@orf=\skip76
\f@nch@height=\skip77
\f@nch@footalignment=\skip78
\f@nch@widthL=\skip79
\f@nch@widthC=\skip80
\f@nch@widthR=\skip81
\@temptokenb=\toks22
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/xcolor\xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-def\pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1353.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\ifxetex.sty
Package: ifxetex 2019/10/25 v0.7 ifxetex legacy package. Use iftex instead.
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/xifthen\xifthen.sty
Package: xifthen 2015/11/05 v1.4.0 Extended ifthen features
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tools\calc.sty
Package: calc 2025/03/01 v4.3b Infix arithmetic (KKT,FJ)
\calc@Acount=\count288
\calc@Bcount=\count289
\calc@Adimen=\dimen160
\calc@Bdimen=\dimen161
\calc@Askip=\skip82
\calc@Bskip=\skip83
LaTeX Info: Redefining \setlength on input line 86.
LaTeX Info: Redefining \addtolength on input line 87.
\calc@Ccount=\count290
\calc@Cskip=\skip84
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\ifthen.sty
Package: ifthen 2024/03/16 v1.1e Standard LaTeX ifthen package (DPC)
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/ifmtarg\ifmtarg.sty
Package: ifmtarg 2018/04/16 v1.2b check for an empty argument
)) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/etoolbox\etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count291
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/setspace\setspace.sty
Package: setspace 2022/12/04 v6.7b set line spacing
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/fontspec\fontspec.sty (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3packages/xparse\xparse.sty (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3kernel\expl3.sty
Package: expl3 2025-07-20 L3 programming layer (loader) 
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3backend\l3backend-pdftex.def
File: l3backend-pdftex.def 2025-06-09 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count292
))
Package: xparse 2024-08-16 L3 Experimental document command parser
)
Package: fontspec 2024/05/11 v2.9e Font selection for XeLaTeX and LuaLaTeX


C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/fontspec\fontspec.sty:101: Fatal Package fontspec Error: The fontspec package requires either XeTeX or
(fontspec)                      LuaTeX.
(fontspec)                      
(fontspec)                      You must change your typesetting engine to,
(fontspec)                      e.g., "xelatex" or "lualatex" instead of
(fontspec)                      "latex" or "pdflatex".

Type <return> to continue.
 ...                                              
                                                  
l.101 \msg_fatal:nn {fontspec} {cannot-use-pdftex}
                                                  

LaTeX does not know anything more about this error, sorry.

Try typing <return> to proceed.
If that doesn't work, type X <return> to quit.

This is a fatal error: LaTeX will abort.



C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/fontspec\fontspec.sty:101: Emergency stop.
<read *> 
         
l.101 \msg_fatal:nn {fontspec} {cannot-use-pdftex}
                                                  
*** (cannot \read from terminal in nonstop modes)

 
Here is how much of TeX's memory you used:
 3218 strings out of 468134
 54137 string characters out of 5430740
 464635 words of memory out of 5000000
 31835 multiletter control sequences out of 15000+600000
 627125 words of font info for 41 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 88i,0n,91p,280b,42s stack positions out of 10000i,1000n,20000p,200000b,200000s
C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/fontspec\fontspec.sty:101:  ==> Fatal error occurred, no output PDF file produced!
