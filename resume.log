This is XeTeX, Version 3.141592653-2.6-0.999997 (MiKTeX 25.4) (preloaded format=xelatex 2025.8.30)  30 AUG 2025 14:22
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**./resume.tex
(resume.tex
LaTeX2e <2025-06-01> patch level 1
L3 programming layer <2025-07-20>
(russell.cls
Document Class: russell 2017/02/05 v1.6.1 russell Curriculum Vitae Class
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\article.cls
Document Class: article 2025/01/22 v1.4n Standard LaTeX document class
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\size11.clo
File: size11.clo 2025/01/22 v1.4n Standard LaTeX file (size option)
)
\c@part=\count271
\c@section=\count272
\c@subsection=\count273
\c@subsubsection=\count274
\c@paragraph=\count275
\c@subparagraph=\count276
\c@figure=\count277
\c@table=\count278
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen148
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tools\array.sty
Package: array 2025/06/08 v2.6j Tabular extension package (FMi)
\col@sep=\dimen149
\ar@mcellbox=\box53
\extrarowheight=\dimen150
\NC@list=\toks17
\extratabsurround=\skip51
\backup@length=\skip52
\ar@cellbox=\box54
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/enumitem\enumitem.sty
Package: enumitem 2025/02/06 v3.11 Customized lists
\enitkv@toks@=\toks18
\labelindent=\skip53
\enit@outerparindent=\dimen151
\enit@toks=\toks19
\enit@inbox=\box55
\enit@count@id=\count279
\enitdp@description=\count280
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/ragged2e\ragged2e.sty
Package: ragged2e 2023/06/22 v3.6 ragged2e Package
\CenteringLeftskip=\skip54
\RaggedLeftLeftskip=\skip55
\RaggedRightLeftskip=\skip56
\CenteringRightskip=\skip57
\RaggedLeftRightskip=\skip58
\RaggedRightRightskip=\skip59
\CenteringParfillskip=\skip60
\RaggedLeftParfillskip=\skip61
\RaggedRightParfillskip=\skip62
\JustifyingParfillskip=\skip63
\CenteringParindent=\skip64
\RaggedLeftParindent=\skip65
\RaggedRightParindent=\skip66
\JustifyingParindent=\skip67
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks20
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
))
\Gm@cnth=\count281
\Gm@cntv=\count282
\c@Gm@tempcnt=\count283
\Gm@bindingoffset=\dimen152
\Gm@wd@mp=\dimen153
\Gm@odd@mp=\dimen154
\Gm@even@mp=\dimen155
\Gm@layoutwidth=\dimen156
\Gm@layoutheight=\dimen157
\Gm@layouthoffset=\dimen158
\Gm@layoutvoffset=\dimen159
\Gm@dimlist=\toks21

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.cfg))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/fancyhdr\fancyhdr.sty
Package: fancyhdr 2025/02/07 v5.2 Extensive control of page headers and footers

\f@nch@headwidth=\skip68
\f@nch@offset@elh=\skip69
\f@nch@offset@erh=\skip70
\f@nch@offset@olh=\skip71
\f@nch@offset@orh=\skip72
\f@nch@offset@elf=\skip73
\f@nch@offset@erf=\skip74
\f@nch@offset@olf=\skip75
\f@nch@offset@orf=\skip76
\f@nch@height=\skip77
\f@nch@footalignment=\skip78
\f@nch@widthL=\skip79
\f@nch@widthC=\skip80
\f@nch@widthR=\skip81
\@temptokenb=\toks22
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/xcolor\xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: xetex.def on input line 274.
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-def\xetex.def
File: xetex.def 2022/09/22 v5.0n Graphics/color driver for xetex
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\mathcolor.lt
x)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\ifxetex.sty
Package: ifxetex 2019/10/25 v0.7 ifxetex legacy package. Use iftex instead.
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/xifthen\xifthen.sty
Package: xifthen 2015/11/05 v1.4.0 Extended ifthen features

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tools\calc.sty
Package: calc 2025/03/01 v4.3b Infix arithmetic (KKT,FJ)
\calc@Acount=\count284
\calc@Bcount=\count285
\calc@Adimen=\dimen160
\calc@Bdimen=\dimen161
\calc@Askip=\skip82
\calc@Bskip=\skip83
LaTeX Info: Redefining \setlength on input line 86.
LaTeX Info: Redefining \addtolength on input line 87.
\calc@Ccount=\count286
\calc@Cskip=\skip84
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\ifthen.sty
Package: ifthen 2024/03/16 v1.1e Standard LaTeX ifthen package (DPC)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/ifmtarg\ifmtarg.sty
Package: ifmtarg 2018/04/16 v1.2b check for an empty argument
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/etoolbox\etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count287
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/setspace\setspace.sty
Package: setspace 2022/12/04 v6.7b set line spacing
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/fontspec\fontspec.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3packages/xparse\xpars
e.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3kernel\expl3.sty
Package: expl3 2025-07-20 L3 programming layer (loader) 

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3backend\l3backend-xet
ex.def
File: l3backend-xetex.def 2025-06-09 L3 backend support: XeTeX
\g__graphics_track_int=\count288
\g__pdfannot_backend_int=\count289
\g__pdfannot_backend_link_int=\count290
))
Package: xparse 2024-08-16 L3 Experimental document command parser
)
Package: fontspec 2024/05/11 v2.9e Font selection for XeLaTeX and LuaLaTeX

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/fontspec\fontspec-xetex
.sty
Package: fontspec-xetex 2024/05/11 v2.9e Font selection for XeLaTeX and LuaLaTe
X
\l__fontspec_script_int=\count291
\l__fontspec_language_int=\count292
\l__fontspec_strnum_int=\count293
\l__fontspec_tmp_int=\count294
\l__fontspec_tmpa_int=\count295
\l__fontspec_tmpb_int=\count296
\l__fontspec_tmpc_int=\count297
\l__fontspec_em_int=\count298
\l__fontspec_emdef_int=\count299
\l__fontspec_strong_int=\count300
\l__fontspec_strongdef_int=\count301
\l__fontspec_tmpa_dim=\dimen162
\l__fontspec_tmpb_dim=\dimen163
\l__fontspec_tmpc_dim=\dimen164
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\fontenc.sty
Package: fontenc 2024/12/21 v2.1c Standard LaTeX package
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/fontspec\fontspec.cfg))
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/unicode-math\unicode-ma
th.sty
Package: unicode-math 2023/08/13 v0.8r Unicode maths in XeLaTeX and LuaLaTeX

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/unicode-math\unicode-ma
th-xetex.sty
Package: unicode-math-xetex 2023/08/13 v0.8r Unicode maths in XeLaTeX and LuaLa
TeX

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3packages/l3keys2e\l3k
eys2e.sty
Package: l3keys2e 2024-08-16 LaTeX2e option processing using LaTeX3 keys
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\fix-cm.sty
Package: fix-cm 2020/11/24 v1.1t fixes to LaTeX

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\ts1enc.def
File: ts1enc.def 2001/06/05 v3.0e (jk/car/fm) Standard LaTeX file
LaTeX Font Info:    Redeclaring font encoding TS1 on input line 47.
LaTeX Encoding Info:    Redeclaring text command \capitalcedilla (encoding TS1)
 on input line 49.
LaTeX Encoding Info:    Redeclaring text command \capitalogonek (encoding TS1) 
on input line 52.
LaTeX Encoding Info:    Redeclaring text command \capitalgrave (encoding TS1) o
n input line 55.
LaTeX Encoding Info:    Redeclaring text command \capitalacute (encoding TS1) o
n input line 56.
LaTeX Encoding Info:    Redeclaring text command \capitalcircumflex (encoding T
S1) on input line 57.
LaTeX Encoding Info:    Redeclaring text command \capitaltilde (encoding TS1) o
n input line 58.
LaTeX Encoding Info:    Redeclaring text command \capitaldieresis (encoding TS1
) on input line 59.
LaTeX Encoding Info:    Redeclaring text command \capitalhungarumlaut (encoding
 TS1) on input line 60.
LaTeX Encoding Info:    Redeclaring text command \capitalring (encoding TS1) on
 input line 61.
LaTeX Encoding Info:    Redeclaring text command \capitalcaron (encoding TS1) o
n input line 62.
LaTeX Encoding Info:    Redeclaring text command \capitalbreve (encoding TS1) o
n input line 63.
LaTeX Encoding Info:    Redeclaring text command \capitalmacron (encoding TS1) 
on input line 64.
LaTeX Encoding Info:    Redeclaring text command \capitaldotaccent (encoding TS
1) on input line 65.
LaTeX Encoding Info:    Redeclaring text command \t (encoding TS1) on input lin
e 66.
LaTeX Encoding Info:    Redeclaring text command \capitaltie (encoding TS1) on 
input line 67.
LaTeX Encoding Info:    Redeclaring text command \newtie (encoding TS1) on inpu
t line 68.
LaTeX Encoding Info:    Redeclaring text command \capitalnewtie (encoding TS1) 
on input line 69.
LaTeX Encoding Info:    Redeclaring text symbol \textcapitalcompwordmark (encod
ing TS1) on input line 70.
LaTeX Encoding Info:    Redeclaring text symbol \textascendercompwordmark (enco
ding TS1) on input line 71.
LaTeX Encoding Info:    Redeclaring text symbol \textquotestraightbase (encodin
g TS1) on input line 72.
LaTeX Encoding Info:    Redeclaring text symbol \textquotestraightdblbase (enco
ding TS1) on input line 73.
LaTeX Encoding Info:    Redeclaring text symbol \texttwelveudash (encoding TS1)
 on input line 74.
LaTeX Encoding Info:    Redeclaring text symbol \textthreequartersemdash (encod
ing TS1) on input line 75.
LaTeX Encoding Info:    Redeclaring text symbol \textleftarrow (encoding TS1) o
n input line 76.
LaTeX Encoding Info:    Redeclaring text symbol \textrightarrow (encoding TS1) 
on input line 77.
LaTeX Encoding Info:    Redeclaring text symbol \textblank (encoding TS1) on in
put line 78.
LaTeX Encoding Info:    Redeclaring text symbol \textdollar (encoding TS1) on i
nput line 79.
LaTeX Encoding Info:    Redeclaring text symbol \textquotesingle (encoding TS1)
 on input line 80.
LaTeX Encoding Info:    Redeclaring text command \textasteriskcentered (encodin
g TS1) on input line 81.
LaTeX Encoding Info:    Redeclaring text symbol \textdblhyphen (encoding TS1) o
n input line 92.
LaTeX Encoding Info:    Redeclaring text symbol \textfractionsolidus (encoding 
TS1) on input line 93.
LaTeX Encoding Info:    Redeclaring text symbol \textzerooldstyle (encoding TS1
) on input line 94.
LaTeX Encoding Info:    Redeclaring text symbol \textoneoldstyle (encoding TS1)
 on input line 95.
LaTeX Encoding Info:    Redeclaring text symbol \texttwooldstyle (encoding TS1)
 on input line 96.
LaTeX Encoding Info:    Redeclaring text symbol \textthreeoldstyle (encoding TS
1) on input line 97.
LaTeX Encoding Info:    Redeclaring text symbol \textfouroldstyle (encoding TS1
) on input line 98.
LaTeX Encoding Info:    Redeclaring text symbol \textfiveoldstyle (encoding TS1
) on input line 99.
LaTeX Encoding Info:    Redeclaring text symbol \textsixoldstyle (encoding TS1)
 on input line 100.
LaTeX Encoding Info:    Redeclaring text symbol \textsevenoldstyle (encoding TS
1) on input line 101.
LaTeX Encoding Info:    Redeclaring text symbol \texteightoldstyle (encoding TS
1) on input line 102.
LaTeX Encoding Info:    Redeclaring text symbol \textnineoldstyle (encoding TS1
) on input line 103.
LaTeX Encoding Info:    Redeclaring text symbol \textlangle (encoding TS1) on i
nput line 104.
LaTeX Encoding Info:    Redeclaring text symbol \textminus (encoding TS1) on in
put line 105.
LaTeX Encoding Info:    Redeclaring text symbol \textrangle (encoding TS1) on i
nput line 106.
LaTeX Encoding Info:    Redeclaring text symbol \textmho (encoding TS1) on inpu
t line 107.
LaTeX Encoding Info:    Redeclaring text symbol \textbigcircle (encoding TS1) o
n input line 108.
LaTeX Encoding Info:    Redeclaring text command \textcircled (encoding TS1) on
 input line 109.
LaTeX Encoding Info:    Redeclaring text symbol \textohm (encoding TS1) on inpu
t line 115.
LaTeX Encoding Info:    Redeclaring text symbol \textlbrackdbl (encoding TS1) o
n input line 116.
LaTeX Encoding Info:    Redeclaring text symbol \textrbrackdbl (encoding TS1) o
n input line 117.
LaTeX Encoding Info:    Redeclaring text symbol \textuparrow (encoding TS1) on 
input line 118.
LaTeX Encoding Info:    Redeclaring text symbol \textdownarrow (encoding TS1) o
n input line 119.
LaTeX Encoding Info:    Redeclaring text symbol \textasciigrave (encoding TS1) 
on input line 120.
LaTeX Encoding Info:    Redeclaring text symbol \textborn (encoding TS1) on inp
ut line 121.
LaTeX Encoding Info:    Redeclaring text symbol \textdivorced (encoding TS1) on
 input line 122.
LaTeX Encoding Info:    Redeclaring text symbol \textdied (encoding TS1) on inp
ut line 123.
LaTeX Encoding Info:    Redeclaring text symbol \textleaf (encoding TS1) on inp
ut line 124.
LaTeX Encoding Info:    Redeclaring text symbol \textmarried (encoding TS1) on 
input line 125.
LaTeX Encoding Info:    Redeclaring text symbol \textmusicalnote (encoding TS1)
 on input line 126.
LaTeX Encoding Info:    Redeclaring text symbol \texttildelow (encoding TS1) on
 input line 127.
LaTeX Encoding Info:    Redeclaring text symbol \textdblhyphenchar (encoding TS
1) on input line 128.
LaTeX Encoding Info:    Redeclaring text symbol \textasciibreve (encoding TS1) 
on input line 129.
LaTeX Encoding Info:    Redeclaring text symbol \textasciicaron (encoding TS1) 
on input line 130.
LaTeX Encoding Info:    Redeclaring text symbol \textacutedbl (encoding TS1) on
 input line 131.
LaTeX Encoding Info:    Redeclaring text symbol \textgravedbl (encoding TS1) on
 input line 132.
LaTeX Encoding Info:    Redeclaring text symbol \textdagger (encoding TS1) on i
nput line 133.
LaTeX Encoding Info:    Redeclaring text symbol \textdaggerdbl (encoding TS1) o
n input line 134.
LaTeX Encoding Info:    Redeclaring text symbol \textbardbl (encoding TS1) on i
nput line 135.
LaTeX Encoding Info:    Redeclaring text symbol \textperthousand (encoding TS1)
 on input line 136.
LaTeX Encoding Info:    Redeclaring text symbol \textbullet (encoding TS1) on i
nput line 137.
LaTeX Encoding Info:    Redeclaring text symbol \textcelsius (encoding TS1) on 
input line 138.
LaTeX Encoding Info:    Redeclaring text symbol \textdollaroldstyle (encoding T
S1) on input line 139.
LaTeX Encoding Info:    Redeclaring text symbol \textcentoldstyle (encoding TS1
) on input line 140.
LaTeX Encoding Info:    Redeclaring text symbol \textflorin (encoding TS1) on i
nput line 141.
LaTeX Encoding Info:    Redeclaring text symbol \textcolonmonetary (encoding TS
1) on input line 142.
LaTeX Encoding Info:    Redeclaring text symbol \textwon (encoding TS1) on inpu
t line 143.
LaTeX Encoding Info:    Redeclaring text symbol \textnaira (encoding TS1) on in
put line 144.
LaTeX Encoding Info:    Redeclaring text symbol \textguarani (encoding TS1) on 
input line 145.
LaTeX Encoding Info:    Redeclaring text symbol \textpeso (encoding TS1) on inp
ut line 146.
LaTeX Encoding Info:    Redeclaring text symbol \textlira (encoding TS1) on inp
ut line 147.
LaTeX Encoding Info:    Redeclaring text symbol \textrecipe (encoding TS1) on i
nput line 148.
LaTeX Encoding Info:    Redeclaring text symbol \textinterrobang (encoding TS1)
 on input line 149.
LaTeX Encoding Info:    Redeclaring text symbol \textinterrobangdown (encoding 
TS1) on input line 150.
LaTeX Encoding Info:    Redeclaring text symbol \textdong (encoding TS1) on inp
ut line 151.
LaTeX Encoding Info:    Redeclaring text symbol \texttrademark (encoding TS1) o
n input line 152.
LaTeX Encoding Info:    Redeclaring text symbol \textpertenthousand (encoding T
S1) on input line 153.
LaTeX Encoding Info:    Redeclaring text symbol \textpilcrow (encoding TS1) on 
input line 154.
LaTeX Encoding Info:    Redeclaring text symbol \textbaht (encoding TS1) on inp
ut line 155.
LaTeX Encoding Info:    Redeclaring text symbol \textnumero (encoding TS1) on i
nput line 156.
LaTeX Encoding Info:    Redeclaring text symbol \textdiscount (encoding TS1) on
 input line 157.
LaTeX Encoding Info:    Redeclaring text symbol \textestimated (encoding TS1) o
n input line 158.
LaTeX Encoding Info:    Redeclaring text symbol \textopenbullet (encoding TS1) 
on input line 159.
LaTeX Encoding Info:    Redeclaring text symbol \textservicemark (encoding TS1)
 on input line 160.
LaTeX Encoding Info:    Redeclaring text symbol \textlquill (encoding TS1) on i
nput line 161.
LaTeX Encoding Info:    Redeclaring text symbol \textrquill (encoding TS1) on i
nput line 162.
LaTeX Encoding Info:    Redeclaring text symbol \textcent (encoding TS1) on inp
ut line 163.
LaTeX Encoding Info:    Redeclaring text symbol \textsterling (encoding TS1) on
 input line 164.
LaTeX Encoding Info:    Redeclaring text symbol \textcurrency (encoding TS1) on
 input line 165.
LaTeX Encoding Info:    Redeclaring text symbol \textyen (encoding TS1) on inpu
t line 166.
LaTeX Encoding Info:    Redeclaring text symbol \textbrokenbar (encoding TS1) o
n input line 167.
LaTeX Encoding Info:    Redeclaring text symbol \textsection (encoding TS1) on 
input line 168.
LaTeX Encoding Info:    Redeclaring text symbol \textasciidieresis (encoding TS
1) on input line 169.
LaTeX Encoding Info:    Redeclaring text symbol \textcopyright (encoding TS1) o
n input line 170.
LaTeX Encoding Info:    Redeclaring text symbol \textordfeminine (encoding TS1)
 on input line 171.
LaTeX Encoding Info:    Redeclaring text symbol \textcopyleft (encoding TS1) on
 input line 172.
LaTeX Encoding Info:    Redeclaring text symbol \textlnot (encoding TS1) on inp
ut line 173.
LaTeX Encoding Info:    Redeclaring text symbol \textcircledP (encoding TS1) on
 input line 174.
LaTeX Encoding Info:    Redeclaring text symbol \textregistered (encoding TS1) 
on input line 175.
LaTeX Encoding Info:    Redeclaring text symbol \textasciimacron (encoding TS1)
 on input line 176.
LaTeX Encoding Info:    Redeclaring text symbol \textdegree (encoding TS1) on i
nput line 177.
LaTeX Encoding Info:    Redeclaring text symbol \textpm (encoding TS1) on input
 line 178.
LaTeX Encoding Info:    Redeclaring text symbol \texttwosuperior (encoding TS1)
 on input line 179.
LaTeX Encoding Info:    Redeclaring text symbol \textthreesuperior (encoding TS
1) on input line 180.
LaTeX Encoding Info:    Redeclaring text symbol \textasciiacute (encoding TS1) 
on input line 181.
LaTeX Encoding Info:    Redeclaring text symbol \textmu (encoding TS1) on input
 line 182.
LaTeX Encoding Info:    Redeclaring text symbol \textparagraph (encoding TS1) o
n input line 183.
LaTeX Encoding Info:    Redeclaring text symbol \textperiodcentered (encoding T
S1) on input line 184.
LaTeX Encoding Info:    Redeclaring text symbol \textreferencemark (encoding TS
1) on input line 185.
LaTeX Encoding Info:    Redeclaring text symbol \textonesuperior (encoding TS1)
 on input line 186.
LaTeX Encoding Info:    Redeclaring text symbol \textordmasculine (encoding TS1
) on input line 187.
LaTeX Encoding Info:    Redeclaring text symbol \textsurd (encoding TS1) on inp
ut line 188.
LaTeX Encoding Info:    Redeclaring text symbol \textonequarter (encoding TS1) 
on input line 189.
LaTeX Encoding Info:    Redeclaring text symbol \textonehalf (encoding TS1) on 
input line 190.
LaTeX Encoding Info:    Redeclaring text symbol \textthreequarters (encoding TS
1) on input line 191.
LaTeX Encoding Info:    Redeclaring text symbol \texteuro (encoding TS1) on inp
ut line 192.
LaTeX Encoding Info:    Redeclaring text symbol \texttimes (encoding TS1) on in
put line 193.
LaTeX Encoding Info:    Redeclaring text symbol \textdiv (encoding TS1) on inpu
t line 194.
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsmath.sty
Package: amsmath 2025/06/16 v2.17y AMS math features
\@mathmargin=\skip85

For additional information on amsmath, use the `?' option.
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amstext.sty
Package: amstext 2024/11/17 v2.01 AMS text

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks23
\ex@=\dimen165
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen166
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count302
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count303
\leftroot@=\count304
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count305
\DOTSCASE@=\count306
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box56
\strutbox@=\box57
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen167
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count307
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count308
\dotsspace@=\muskip17
\c@parentequation=\count309
\dspbrk@lvl=\count310
\tag@help=\toks24
\row@=\count311
\column@=\count312
\maxfields@=\count313
\andhelp@=\toks25
\eqnshift@=\dimen168
\alignsep@=\dimen169
\tagshift@=\dimen170
\tagwidth@=\dimen171
\totwidth@=\dimen172
\lineht@=\dimen173
\@envbody=\toks26
\multlinegap=\skip86
\multlinetaggap=\skip87
\mathdisplay@stack=\toks27
LaTeX Info: Redefining \[ on input line 2949.
LaTeX Info: Redefining \] on input line 2950.
)
\g__um_fam_int=\count314
\g__um_fonts_used_int=\count315
\l__um_primecount_int=\count316
\g__um_primekern_muskip=\muskip18

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/unicode-math\unicode-ma
th-table.tex)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/fontawesome5\fontawesom
e5.sty
Package: fontawesome5 2022/05/02 v5.15.4 Font Awesome 5

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/fontawesome5\fontawesom
e5-utex-helper.sty
Package: fontawesome5-utex-helper 2022/05/02 v5.15.4 uTeX helper for fontawesom
e5
LaTeX Font Info:    Trying to load font information for TU+fontawesomefree on i
nput line 69.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/fontawesome5\tufontawes
omefree.fd)
LaTeX Font Info:    Trying to load font information for TU+fontawesomebrands on
 input line 70.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/fontawesome5\tufontawes
omebrands.fd)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/roboto\roboto.sty
Package: roboto 2022/09/10 (Bob Tennent) Supports Roboto fonts for all LaTeX en
gines.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\ifluatex.sty
Package: ifluatex 2019/10/25 v1.5 ifluatex legacy package. Use iftex instead.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/xkeyval\xkeyval.sty
Package: xkeyval 2022/06/16 v2.9 package option processing (HA)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/xkeyval\xkeyval.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/xkeyval\xkvutils.tex
\XKV@toks=\toks28
\XKV@tempa@toks=\toks29
)
\XKV@depth=\count317
File: xkeyval.tex 2014/12/03 v2.7a key=value parser (HA)
)) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\textcomp.sty
Package: textcomp 2024/04/24 v2.1b Standard LaTeX package
)
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> TU/Roboto(1)/m/n on input line 150.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> TU/Roboto(1)/bx/n on input line 150.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/sourcesanspro\sourcesan
spro.sty
Package: sourcesanspro 2018/05/19 v2.7 Adobe's Source Sans Pro typeface
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tcolorbox\tcolorbox.sty
Package: tcolorbox 2025/07/08 version 6.7.1 text color boxes

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/frontendlayer\tikz.
sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/basiclayer\pgf.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/utilities\pgfrcs.st
y
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfutil
-common.tex
\pgfutil@everybye=\toks30
\pgfutil@tempdima=\dimen174
\pgfutil@tempdimb=\dimen175
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfutil
-latex.def
\pgfutil@abb=\box58
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfrcs.
code.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf\pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
Package: pgf 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/basiclayer\pgfcore.
sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphicx.sty
Package: graphicx 2024/12/31 v1.2e Enhanced LaTeX Graphics (DPC,SPQR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\graphics.c
fg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: xetex.def on input line 106.
)
\Gin@req@height=\dimen176
\Gin@req@width=\dimen177
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/systemlayer\pgfsys.
sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgfsy
s.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfkeys
.code.tex
\pgfkeys@pathtoks=\toks31
\pgfkeys@temptoks=\toks32

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfkeys
libraryfiltered.code.tex
\pgfkeys@tmptoks=\toks33
))
\pgf@x=\dimen178
\pgf@y=\dimen179
\pgf@xa=\dimen180
\pgf@ya=\dimen181
\pgf@xb=\dimen182
\pgf@yb=\dimen183
\pgf@xc=\dimen184
\pgf@yc=\dimen185
\pgf@xd=\dimen186
\pgf@yd=\dimen187
\w@pgf@writea=\write3
\r@pgf@reada=\read2
\c@pgf@counta=\count318
\c@pgf@countb=\count319
\c@pgf@countc=\count320
\c@pgf@countd=\count321
\t@pgf@toka=\toks34
\t@pgf@tokb=\toks35
\t@pgf@tokc=\toks36
\pgf@sys@id@count=\count322

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgf.c
fg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-xetex.def

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgfsy
s-xetex.def
File: pgfsys-xetex.def 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgfsy
s-dvipdfmx.def
File: pgfsys-dvipdfmx.def 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgfsy
s-common-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
)
\pgfsys@objnum=\count323
)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgfsy
ssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count324
\pgfsyssoftpath@bigbuffer@items=\count325
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgfsy
sprotocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
e.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmath.code
.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathutil.
code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathparse
r.code.tex
\pgfmath@dimen=\dimen188
\pgfmath@count=\count326
\pgfmath@box=\box59
\pgfmath@toks=\toks37
\pgfmath@stack@operand=\toks38
\pgfmath@stack@operation=\toks39
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.basic.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.trigonometric.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.random.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.comparison.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.base.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.round.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.misc.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.integerarithmetics.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathcalc.
code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfloat
.code.tex
\c@pgfmathroundto@lastzeros=\count327
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfint.code.
tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
epoints.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen189
\pgf@picmaxx=\dimen190
\pgf@picminy=\dimen191
\pgf@picmaxy=\dimen192
\pgf@pathminx=\dimen193
\pgf@pathmaxx=\dimen194
\pgf@pathminy=\dimen195
\pgf@pathmaxy=\dimen196
\pgf@xx=\dimen197
\pgf@xy=\dimen198
\pgf@yx=\dimen199
\pgf@yy=\dimen256
\pgf@zx=\dimen257
\pgf@zy=\dimen258
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
epathconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen259
\pgf@path@lasty=\dimen260
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
epathusage.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen261
\pgf@shorten@start@additional=\dimen262
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
escopes.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box60
\pgf@hbox=\box61
\pgf@layerbox@main=\box62
\pgf@picture@serial@count=\count328
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
egraphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen263
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
etransformations.code.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen264
\pgf@pt@y=\dimen265
\pgf@pt@temp=\dimen266
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
equick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
eobjects.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
epathprocessing.code.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
earrows.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen267
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
eshade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen268
\pgf@sys@shading@range@num=\count329
\pgf@shadingcount=\count330
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
eimage.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
eexternal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box63
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
elayers.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
etransparency.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
epatterns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
erdf.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/modules\pgfmodule
shapes.code.tex
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box64
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/modules\pgfmodule
plot.code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/compatibility\pgfco
mp-version-0-65.sty
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen269
\pgf@nodesepend=\dimen270
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/compatibility\pgfco
mp-version-1-18.sty
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/utilities\pgffor.st
y
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/utilities\pgfkeys.s
ty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfkeys
.code.tex))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/math\pgfmath.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmath.code
.tex))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgffor.
code.tex
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen271
\pgffor@skip=\dimen272
\pgffor@stack=\toks40
\pgffor@toks=\toks41
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tik
z\tikz.code.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/libraries\pgflibr
aryplothandlers.code.tex
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count331
\pgfplotmarksize=\dimen273
)
\tikz@lastx=\dimen274
\tikz@lasty=\dimen275
\tikz@lastxsaved=\dimen276
\tikz@lastysaved=\dimen277
\tikz@lastmovetox=\dimen278
\tikz@lastmovetoy=\dimen279
\tikzleveldistance=\dimen280
\tikzsiblingdistance=\dimen281
\tikz@figbox=\box65
\tikz@figbox@bg=\box66
\tikz@tempbox=\box67
\tikz@tempbox@bg=\box68
\tikztreelevel=\count332
\tikznumberofchildren=\count333
\tikznumberofcurrentchild=\count334
\tikz@fig@count=\count335

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/modules\pgfmodule
matrix.code.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count336
\pgfmatrixcurrentcolumn=\count337
\pgf@matrix@numberofcolumns=\count338
)
\tikz@expandcount=\count339

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tik
z/libraries\tikzlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tools\verbatim.sty
Package: verbatim 2024-01-22 v1.5x LaTeX2e package for verbatim enhancements
\every@verbatim=\toks42
\verbatim@line=\toks43
\verbatim@in@stream=\read3
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/environ\environ.sty
Package: environ 2014/05/04 v0.3 A new way to define environments

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/trimspaces\trimspaces.s
ty
Package: trimspaces 2009/09/17 v1.1 Trim spaces around a token list
))
\tcb@titlebox=\box69
\tcb@upperbox=\box70
\tcb@lowerbox=\box71
\tcb@phantombox=\box72
\c@tcbbreakpart=\count340
\c@tcblayer=\count341
\c@tcolorbox@number=\count342
\l__tcobox_tmpa_box=\box73
\l__tcobox_tmpa_dim=\dimen282
\tcb@temp=\box74
\tcb@temp=\box75
\tcb@temp=\box76
\tcb@temp=\box77

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tcolorbox\tcbskins.code
.tex
Library (tcolorbox): 'tcbskins.code.tex' version '6.7.1'

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tikzfill\tikzfill.image
.sty
Package: tikzfill.image 2023/08/08 v1.0.1 Image filling library for TikZ

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tikzfill\tikzfill-commo
n.sty
Package: tikzfill-common 2023/08/08 v1.0.1 Auxiliary code for tikzfill
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tikzfill\tikzlibraryfil
l.image.code.tex
File: tikzlibraryfill.image.code.tex 2023/08/08 v1.0.1 Image filling library
\l__tikzfill_img_box=\box78
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tcolorbox\tcbskinsjigsa
w.code.tex
Library (tcolorbox): 'tcbskinsjigsaw.code.tex' version '6.7.1'
))) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/parskip\parskip.sty
Package: parskip 2021-03-14 v2.0h non-zero parskip adjustments

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvoptions\kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/ltxcmds\ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvsetkeys\kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\hyperref.sty
Package: hyperref 2025-07-12 v7.01o Hypertext links for LaTeX

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/kvdefinekeys\kvdefine
keys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pdfescape\pdfescape.s
ty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pdftexcmds\pdftexcmds
.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/infwarerr\infwarerr.s
ty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode not found.
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hycolor\hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\nameref.sty
Package: nameref 2025-06-21 v2.57 Cross-referencing by name of section

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/refcount\refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/gettitlestring\gettit
lestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
)
\c@section@level=\count343
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/stringenc\stringenc.s
ty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO
)
)
\@linkdim=\dimen283
\Hy@linkcounter=\count344
\Hy@pagecounter=\count345
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\pd1enc.def
File: pd1enc.def 2025-07-12 v7.01o Hyperref: PDFDocEncoding definition (HO)
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/intcalc\intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count346
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\puenc.def
File: puenc.def 2025-07-12 v7.01o Hyperref: PDF Unicode definition (HO)
)
Package hyperref Info: Option `unicode' set `true' on input line 4066.
Package hyperref Info: Option `pdfpagelabels' set `false' on input line 4066.
Package hyperref Info: Hyper figures OFF on input line 4195.
Package hyperref Info: Link nesting OFF on input line 4200.
Package hyperref Info: Hyper index ON on input line 4203.
Package hyperref Info: Plain pages OFF on input line 4210.
Package hyperref Info: Backreferencing OFF on input line 4215.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4462.
\c@Hy@tempcnt=\count347

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/url\url.sty
\Urlmuskip=\muskip19
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4801.
\XeTeXLinkMargin=\dimen284

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/bitset\bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/bigintcalc\bigintcalc
.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO
)
))
\Fld@menulength=\count348
\Field@Width=\dimen285
\Fld@charsize=\dimen286
Package hyperref Info: Hyper figures OFF on input line 6078.
Package hyperref Info: Link nesting OFF on input line 6083.
Package hyperref Info: Hyper index ON on input line 6086.
Package hyperref Info: backreferencing OFF on input line 6093.
Package hyperref Info: Link coloring OFF on input line 6098.
Package hyperref Info: Link coloring with OCG OFF on input line 6103.
Package hyperref Info: PDF/A mode OFF on input line 6108.
\c@Item=\count349
\c@Hfootnote=\count350
)
Package hyperref Info: Driver (autodetected): hxetex.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\hxetex.def
File: hxetex.def 2025-07-12 v7.01o Hyperref driver for XeTeX
\pdfm@box=\box79
\c@Hy@AnnotLevel=\count351
\HyField@AnnotCount=\count352
\Fld@listcount=\count353
\c@bookmark@seq@number=\count354

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/rerunfilecheck\rerunfil
echeck.sty
Package: rerunfilecheck 2025-06-21 v1.11 Rerun checks for auxiliary files (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/uniquecounter\uniquec
ounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 2
84.
)
\Hy@SectionHShift=\skip88
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/bookmark\bookmark.sty
Package: bookmark 2023-12-10 v1.31 PDF bookmarks (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/bookmark\bkm-dvipdfm.de
f
File: bkm-dvipdfm.def 2023-12-10 v1.31 bookmark driver for dvipdfm (HO)
\BKM@id=\count355
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/biblatex\biblatex.sty
Package: biblatex 2025/07/10 v3.21 programmable bibliographies (PK/MW)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/logreq\logreq.sty
Package: logreq 2010/08/04 v1.0 xml request logger
\lrq@indent=\count356

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/logreq\logreq.def
File: logreq.def 2010/08/04 v1.0 logreq spec v1.0
))
\c@tabx@nest=\count357
\c@listtotal=\count358
\c@listcount=\count359
\c@liststart=\count360
\c@liststop=\count361
\c@citecount=\count362
\c@citetotal=\count363
\c@multicitecount=\count364
\c@multicitetotal=\count365
\c@instcount=\count366
\c@maxnames=\count367
\c@minnames=\count368
\c@maxitems=\count369
\c@minitems=\count370
\c@citecounter=\count371
\c@maxcitecounter=\count372
\c@savedcitecounter=\count373
\c@uniquelist=\count374
\c@uniquename=\count375
\c@refsection=\count376
\c@refsegment=\count377
\c@maxextratitle=\count378
\c@maxextratitleyear=\count379
\c@maxextraname=\count380
\c@maxextradate=\count381
\c@maxextraalpha=\count382
\c@abbrvpenalty=\count383
\c@highnamepenalty=\count384
\c@lownamepenalty=\count385
\c@maxparens=\count386
\c@parenlevel=\count387
\blx@tempcnta=\count388
\blx@tempcntb=\count389
\blx@tempcntc=\count390
\c@blx@maxsection=\count391
\blx@maxsegment@0=\count392
\blx@notetype=\count393
\blx@parenlevel@text=\count394
\blx@parenlevel@foot=\count395
\blx@sectionciteorder@0=\count396
\blx@sectionciteorderinternal@0=\count397
\blx@entrysetcounter=\count398
\blx@biblioinstance=\count399
\labelnumberwidth=\skip89
\labelalphawidth=\skip90
\biblabelsep=\skip91
\bibitemsep=\skip92
\bibnamesep=\skip93
\bibinitsep=\skip94
\bibparsep=\skip95
\bibhang=\skip96
\blx@bcfin=\read4
\blx@bcfout=\write4
\blx@langwohyphens=\language79
\c@mincomprange=\count400
\c@maxcomprange=\count401
\c@mincompwidth=\count402
Package biblatex Info: Trying to load biblatex default data model...
Package biblatex Info: ... file 'blx-dm.def' found.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/biblatex\blx-dm.def
File: blx-dm.def 2025/07/10 v3.21 biblatex datamodel (PK/MW)
)
Package biblatex Info: Trying to load biblatex style data model...
Package biblatex Info: ... file 'verbose.dbx' not found.
Package biblatex Info: Trying to load biblatex custom data model...
Package biblatex Info: ... file 'biblatex-dm.cfg' not found.
\c@afterword=\count403
\c@savedafterword=\count404
\c@annotator=\count405
\c@savedannotator=\count406
\c@author=\count407
\c@savedauthor=\count408
\c@bookauthor=\count409
\c@savedbookauthor=\count410
\c@commentator=\count411
\c@savedcommentator=\count412
\c@editor=\count413
\c@savededitor=\count414
\c@editora=\count415
\c@savededitora=\count416
\c@editorb=\count417
\c@savededitorb=\count418
\c@editorc=\count419
\c@savededitorc=\count420
\c@foreword=\count421
\c@savedforeword=\count422
\c@holder=\count423
\c@savedholder=\count424
\c@introduction=\count425
\c@savedintroduction=\count426
\c@namea=\count427
\c@savednamea=\count428
\c@nameb=\count429
\c@savednameb=\count430
\c@namec=\count431
\c@savednamec=\count432
\c@translator=\count433
\c@savedtranslator=\count434
\c@shortauthor=\count435
\c@savedshortauthor=\count436
\c@shorteditor=\count437
\c@savedshorteditor=\count438
\c@labelname=\count439
\c@savedlabelname=\count440
\c@institution=\count441
\c@savedinstitution=\count442
\c@lista=\count443
\c@savedlista=\count444
\c@listb=\count445
\c@savedlistb=\count446
\c@listc=\count447
\c@savedlistc=\count448
\c@listd=\count449
\c@savedlistd=\count450
\c@liste=\count451
\c@savedliste=\count452
\c@listf=\count453
\c@savedlistf=\count454
\c@location=\count455
\c@savedlocation=\count456
\c@organization=\count457
\c@savedorganization=\count458
\c@origlocation=\count459
\c@savedoriglocation=\count460
\c@origpublisher=\count461
\c@savedorigpublisher=\count462
\c@publisher=\count463
\c@savedpublisher=\count464
\c@language=\count465
\c@savedlanguage=\count466
\c@origlanguage=\count467
\c@savedoriglanguage=\count468
\c@pageref=\count469
\c@savedpageref=\count470
\shorthandwidth=\skip97
\shortjournalwidth=\skip98
\shortserieswidth=\skip99
\shorttitlewidth=\skip100
\shortauthorwidth=\skip101
\shorteditorwidth=\skip102
\locallabelnumberwidth=\skip103
\locallabelalphawidth=\skip104
\localshorthandwidth=\skip105
\localshortjournalwidth=\skip106
\localshortserieswidth=\skip107
\localshorttitlewidth=\skip108
\localshortauthorwidth=\skip109
\localshorteditorwidth=\skip110
Package biblatex Info: Trying to load enhanced support for Unicode engines...
Package biblatex Info: ... file 'blx-unicode.def' found.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/biblatex\blx-unicode.de
f)
Package biblatex Info: Trying to load compatibility code...
Package biblatex Info: ... file 'blx-compat.def' found.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/biblatex\blx-compat.def
File: blx-compat.def 2025/07/10 v3.21 biblatex compatibility (PK/MW)
)
Package biblatex Info: Trying to load generic definitions...
Package biblatex Info: ... file 'biblatex.def' found.
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/biblatex\biblatex.def
File: biblatex.def 2025/07/10 v3.21 biblatex compatibility (PK/MW)
\c@textcitecount=\count471
\c@textcitetotal=\count472
\c@textcitemaxnames=\count473
\c@biburlbigbreakpenalty=\count474
\c@biburlbreakpenalty=\count475
\c@biburlnumpenalty=\count476
\c@biburlucpenalty=\count477
\c@biburllcpenalty=\count478
\biburlbigskip=\muskip20
\biburlnumskip=\muskip21
\biburlucskip=\muskip22
\biburllcskip=\muskip23
\c@smartand=\count479
)
Package biblatex Info: Trying to load bibliography style 'verbose'...
Package biblatex Info: ... file 'verbose.bbx' found.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/biblatex/bbx\verbose.bb
x
File: verbose.bbx 2025/07/10 v3.21 biblatex bibliography style (PK/MW)
Package biblatex Info: Trying to load bibliography style 'authortitle'...
Package biblatex Info: ... file 'authortitle.bbx' found.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/biblatex/bbx\authortitl
e.bbx
File: authortitle.bbx 2025/07/10 v3.21 biblatex bibliography style (PK/MW)
Package biblatex Info: Trying to load bibliography style 'standard'...
Package biblatex Info: ... file 'standard.bbx' found.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/biblatex/bbx\standard.b
bx
File: standard.bbx 2025/07/10 v3.21 biblatex bibliography style (PK/MW)
\c@bbx:relatedcount=\count480
\c@bbx:relatedtotal=\count481
)))
Package biblatex Info: Trying to load citation style 'verbose'...
Package biblatex Info: ... file 'verbose.cbx' found.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/biblatex/cbx\verbose.cb
x
File: verbose.cbx 2025/07/10 v3.21 biblatex citation style (PK/MW)
Package biblatex Info: Redefining '\cite'.
Package biblatex Info: Redefining '\parencite'.
Package biblatex Info: Redefining '\footcite'.
Package biblatex Info: Redefining '\footcitetext'.
Package biblatex Info: Redefining '\smartcite'.
Package biblatex Info: Redefining '\textcite'.
Package biblatex Info: Redefining '\textcites'.
)
Package biblatex Info: Trying to load configuration file...
Package biblatex Info: ... file 'biblatex.cfg' found.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/biblatex\biblatex.cfg
File: biblatex.cfg 
)
Package biblatex Info: XeTeX detected.
(biblatex)             Assuming input encoding 'utf8'.
Package biblatex Info: Document encoding is UTF8 ....
Package biblatex Info: ... and expl3
(biblatex)             2025-07-20 L3 programming layer (loader) 
(biblatex)             is new enough (at least 2020/04/06),
(biblatex)             setting 'casechanger=expl3'.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/biblatex\blx-case-expl3
.sty
Package: blx-case-expl3 2025/07/10 v3.21 expl3 case changing code for biblatex
)))
\@quotelevel=\count482
\@quotereset=\count483
LaTeX Font Info:    Font shape `TU/SourceSansPro(0)/m/n' will be
(Font)              scaled to size 10.95pt on input line 71.
 (resume.aux)
\openout1 = `resume.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 71.
LaTeX Font Info:    ... okay on input line 71.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 71.
LaTeX Font Info:    ... okay on input line 71.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 71.
LaTeX Font Info:    ... okay on input line 71.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 71.
LaTeX Font Info:    ... okay on input line 71.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 71.
LaTeX Font Info:    ... okay on input line 71.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 71.
LaTeX Font Info:    ... okay on input line 71.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 71.
LaTeX Font Info:    ... okay on input line 71.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 71.
LaTeX Font Info:    ... okay on input line 71.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 71.
LaTeX Font Info:    ... okay on input line 71.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 71.
LaTeX Font Info:    ... okay on input line 71.

*geometry* driver: auto-detecting
*geometry* detected driver: xetex
*geometry* verbose mode - [ preamble ] result:
* driver: xetex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(39.83368pt, 517.84052pt, 39.83368pt)
* v-part:(T,H,B)=(22.76228pt, 771.06952pt, 51.21504pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=517.84052pt
* \textheight=771.06952pt
* \oddsidemargin=-32.43631pt
* \evensidemargin=-32.43631pt
* \topmargin=-86.5077pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=11.0pt
* \footskip=14.22636pt
* \marginparwidth=50.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

LaTeX Font Info:    Overwriting math alphabet `\mathrm' in version `normal'
(Font)                  OT1/cmr/m/n --> TU/lmr/m/n on input line 71.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> TU/lmr/m/it on input line 71.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> TU/lmr/bx/n on input line 71.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/m/n on input line 71.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/bx/n on input line 71.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `normal' on input line 71.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> TU/latinmodern-math.otf(1)/m/n on input
 line 71.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `bold' on input line 71.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> TU/latinmodern-math.otf(1)/b/n on inpu
t line 71.
LaTeX Font Info:    Encoding `OMS' has changed to `TU' for symbol font
(Font)              `symbols' in the math version `normal' on input line 71.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `normal'
(Font)                  OMS/cmsy/m/n --> TU/latinmodern-math.otf(2)/m/n on inpu
t line 71.
LaTeX Font Info:    Encoding `OMS' has changed to `TU' for symbol font
(Font)              `symbols' in the math version `bold' on input line 71.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  OMS/cmsy/b/n --> TU/latinmodern-math.otf(2)/b/n on inpu
t line 71.
LaTeX Font Info:    Encoding `OMX' has changed to `TU' for symbol font
(Font)              `largesymbols' in the math version `normal' on input line 7
1.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `normal'
(Font)                  OMX/cmex/m/n --> TU/latinmodern-math.otf(3)/m/n on inpu
t line 71.
LaTeX Font Info:    Encoding `OMX' has changed to `TU' for symbol font
(Font)              `largesymbols' in the math version `bold' on input line 71.

LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  OMX/cmex/m/n --> TU/latinmodern-math.otf(3)/b/n on inpu
t line 71.
Package hyperref Info: Link coloring OFF on input line 71.
Package biblatex Info: Trying to load language 'english'...
Package biblatex Info: ... file 'english.lbx' found.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/biblatex/lbx\english.lb
x
File: english.lbx 2025/07/10 v3.21 biblatex localization (PK/MW)
)
Package biblatex Info: XeTeX detected.
(biblatex)             Assuming input encoding 'utf8'.
Package biblatex Info: Automatic encoding selection.
(biblatex)             Assuming data encoding 'utf8'.
\openout4 = `resume.bcf'.

Package biblatex Info: Trying to load bibliographic data...
Package biblatex Info: ... file 'resume.bbl' not found.

No file resume.bbl.
Package biblatex Info: Reference section=0 on input line 71.
Package biblatex Info: Reference segment=0 on input line 71.
\headertextwidth=\skip111
\headerphotowidth=\skip112
LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(2)/m/n' will be
(Font)              scaled to size 10.95116pt on input line 76.
LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(2)/m/n' will be
(Font)              scaled to size 7.6658pt on input line 76.
LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(2)/m/n' will be
(Font)              scaled to size 5.47559pt on input line 76.
LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(3)/m/n' will be
(Font)              scaled to size 10.94882pt on input line 76.
LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(3)/m/n' will be
(Font)              scaled to size 7.66417pt on input line 76.
LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(3)/m/n' will be
(Font)              scaled to size 5.47441pt on input line 76.
LaTeX Font Info:    Font shape `TU/Roboto(1)/m/n' will be
(Font)              scaled to size 20.0pt on input line 76.
LaTeX Font Info:    Font shape `TU/SourceSansPro(1)/m/n' will be
(Font)              scaled to size 7.6pt on input line 76.
LaTeX Font Info:    Font shape `TU/SourceSansPro(1)/m/sc' will be
(Font)              scaled to size 7.6pt on input line 76.
LaTeX Font Info:    Font shape `TU/Roboto(1)/m/n' will be
(Font)              scaled to size 8.0pt on input line 76.
LaTeX Font Info:    Font shape `TU/Roboto(1)/m/it' will be
(Font)              scaled to size 8.0pt on input line 76.
LaTeX Font Info:    Font shape `TU/Roboto(1)/m/n' will be
(Font)              scaled to size 6.8pt on input line 76.

Overfull \hbox (1.46pt too wide) in paragraph at lines 76--76
[]$[]$ $[]$$[]$ 
 []

(cv/summary.tex
LaTeX Font Info:    Font shape `TU/SourceSansPro(1)/m/n' will be
(Font)              scaled to size 16.0pt on input line 4.
LaTeX Font Info:    Font shape `TU/SourceSansPro(1)/b/n' will be
(Font)              scaled to size 16.0pt on input line 4.
LaTeX Font Info:    Font shape `TU/SourceSansPro(2)/m/n' will be
(Font)              scaled to size 9.0pt on input line 10.
) (cv/education.tex
LaTeX Font Info:    Font shape `TU/SourceSansPro(1)/m/n' will be
(Font)              scaled to size 10.0pt on input line 23.
LaTeX Font Info:    Font shape `TU/SourceSansPro(1)/b/n' will be
(Font)              scaled to size 10.0pt on input line 23.
LaTeX Font Info:    Font shape `TU/SourceSansPro(2)/m/sl' in size <9> not avail
able
(Font)              Font shape `TU/SourceSansPro(2)/m/it' tried instead on inpu
t line 23.
LaTeX Font Info:    Font shape `TU/SourceSansPro(2)/m/it' will be
(Font)              scaled to size 9.0pt on input line 23.
LaTeX Font Info:    Font shape `TU/SourceSansPro(1)/m/n' will be
(Font)              scaled to size 9.0pt on input line 23.
LaTeX Font Info:    Font shape `TU/SourceSansPro(2)/m/n' will be
(Font)              scaled to size 8.0pt on input line 23.
LaTeX Font Info:    Font shape `TU/SourceSansPro(2)/m/sl' in size <8> not avail
able
(Font)              Font shape `TU/SourceSansPro(2)/m/it' tried instead on inpu
t line 23.
LaTeX Font Info:    Font shape `TU/SourceSansPro(2)/m/it' will be
(Font)              scaled to size 8.0pt on input line 23.
) (cv/experience.tex
LaTeX Font Info:    Font shape `TU/SourceSansPro(2)/b/n' will be
(Font)              scaled to size 9.0pt on input line 25.
) (cv/projects.tex)
(cv/skills.tex
Overfull \hbox (26.5079pt too wide) in alignment at lines 10--25
[] [] 
 []

) (cv/interests.tex

LaTeX Font Info:    Font shape `TU/SourceSansPro(0)/m/n' will be
(Font)              scaled to size 16.0pt on input line 6.
LaTeX Font Info:    Font shape `TU/SourceSansPro(1)/m/n' will be
(Font)              scaled to size 8.0pt on input line 6.
LaTeX Font Info:    Font shape `TU/SourceSansPro(1)/m/sc' will be
(Font)              scaled to size 8.0pt on input line 6.
[1

]
Overfull \hbox (23.2579pt too wide) in alignment at lines 12--27
[] [] 
 []

) (cv/languages.tex
Overfull \hbox (16.0879pt too wide) in alignment at lines 9--21
[] [] 
 []

)

[2] (resume.aux)
 ***********
LaTeX2e <2025-06-01> patch level 1
L3 programming layer <2025-07-20>
 ***********


LaTeX Warning: There were undefined references.


Package biblatex Warning: Please (re)run Biber on the file:
(biblatex)                resume
(biblatex)                and rerun LaTeX afterwards.

Package logreq Info: Writing requests to 'resume.run.xml'.
\openout1 = `resume.run.xml'.

 ) 
Here is how much of TeX's memory you used:
 40198 strings out of 403250
 809071 string characters out of 5460888
 1833251 words of memory out of 5000000
 68068 multiletter control sequences out of 15000+600000
 629669 words of font info for 135 fonts, out of 8000000 for 9000
 1348 hyphenation exceptions out of 8191
 131i,12n,135p,566b,852s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on resume.pdf (2 pages).
