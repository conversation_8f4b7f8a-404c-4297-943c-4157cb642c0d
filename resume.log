This is LuaHBTeX, Version 1.22.0 (MiKTeX 25.4) (format=lualatex 2025.8.30)  30 AUG 2025 14:33
 restricted system commands enabled.
 file:line:error style messages enabled.
**d:/workspace/CV/resume.tex
(d:/workspace/CV/resume.tex
LaTeX2e <2025-06-01> patch level 1
L3 programming layer <2025-07-20>
Lua module: luaotfload 2024-12-03 v3.29 Lua based OpenType font support
Lua module: lualibs 2023-07-13 v2.76 ConTeXt Lua standard libraries.
Lua module: lualibs-extended 2023-07-13 v2.76 ConTeXt Lua libraries -- extended 
collection.
luaotfload | conf : Root cache directory is "C:/Users/<USER>/AppData/Local/MiKTeX
/luatex-cache/generic/names".
luaotfload | init : Loading fontloader "fontloader-2023-12-28.lua" from kpse-res
olved path "C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/luatex/luaotfload/f
ontloader-2023-12-28.lua".
Lua-only attribute luaotfload@noligature = 1
luaotfload | init : Context OpenType loader version 3.134
Inserting `luaotfload.node_processor' in `pre_linebreak_filter'.
Inserting `luaotfload.node_processor' in `hpack_filter'.
Inserting `luaotfload.glyph_stream' in `glyph_stream_provider'.
Inserting `luaotfload.define_font' in `define_font'.
Lua-only attribute luaotfload_color_attribute = 2
luaotfload | conf : Root cache directory is "C:/Users/<USER>/AppData/Local/MiKTeX
/luatex-cache/generic/names".
Inserting `luaotfload.harf.strip_prefix' in `find_opentype_file'.
Inserting `luaotfload.harf.strip_prefix' in `find_truetype_file'.
Removing  `luaotfload.glyph_stream' from `glyph_stream_provider'.
Inserting `luaotfload.harf.glyphstream' in `glyph_stream_provider'.
Inserting `luaotfload.harf.finalize_vlist' in `post_linebreak_filter'.
Inserting `luaotfload.harf.finalize_hlist' in `hpack_filter'.
Inserting `luaotfload.cleanup_files' in `wrapup_run'.
Inserting `luaotfload.harf.finalize_unicode' in `finish_pdffile'.
Inserting `luaotfload.glyphinfo' in `glyph_info'.
Lua-only attribute luaotfload.letterspace_done = 3
Inserting `luaotfload.aux.set_sscale_dimens' in `luaotfload.patch_font'.
Inserting `luaotfload.aux.set_font_index' in `luaotfload.patch_font'.
Inserting `luaotfload.aux.patch_cambria_domh' in `luaotfload.patch_font'.
Inserting `luaotfload.aux.fixup_fontdata' in `luaotfload.patch_font_unsafe'.
Inserting `luaotfload.aux.set_capheight' in `luaotfload.patch_font'.
Inserting `luaotfload.aux.set_xheight' in `luaotfload.patch_font'.
Inserting `luaotfload.rewrite_fontname' in `luaotfload.patch_font'.
Inserting `tracingstacklevels' in `input_level_string'. (./russell.cls
Document Class: russell 2017/02/05 v1.6.1 russell Curriculum Vitae Class
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/base/article.cls
Document Class: article 2025/01/22 v1.4n Standard LaTeX document class
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/base/size11.clo
File: size11.clo 2025/01/22 v1.4n Standard LaTeX file (size option)
luaotfload | db : Font names database loaded from C:/Users/<USER>/AppData/Local/M
iKTeX/luatex-cache/generic/names/luaotfload-names.luc.gz)
\c@part=\count273
\c@section=\count274
\c@subsection=\count275
\c@subsubsection=\count276
\c@paragraph=\count277
\c@subparagraph=\count278
\c@figure=\count279
\c@table=\count280
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen147
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/tools/array.sty
Package: array 2025/06/08 v2.6j Tabular extension package (FMi)
\col@sep=\dimen148
\ar@mcellbox=\box53
\extrarowheight=\dimen149
\NC@list=\toks17
\extratabsurround=\skip51
\backup@length=\skip52
\ar@cellbox=\box54
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/enumitem/enumitem.sty
Package: enumitem 2025/02/06 v3.11 Customized lists
\enitkv@toks@=\toks18
\labelindent=\skip53
\enit@outerparindent=\dimen150
\enit@toks=\toks19
\enit@inbox=\box55
\enit@count@id=\count281
\enitdp@description=\count282
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/ragged2e/ragged2e.sty
Package: ragged2e 2023/06/22 v3.6 ragged2e Package
\CenteringLeftskip=\skip54
\RaggedLeftLeftskip=\skip55
\RaggedRightLeftskip=\skip56
\CenteringRightskip=\skip57
\RaggedLeftRightskip=\skip58
\RaggedRightRightskip=\skip59
\CenteringParfillskip=\skip60
\RaggedLeftParfillskip=\skip61
\RaggedRightParfillskip=\skip62
\JustifyingParfillskip=\skip63
\CenteringParindent=\skip64
\RaggedLeftParindent=\skip65
\RaggedRightParindent=\skip66
\JustifyingParindent=\skip67
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks20
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
))
\Gm@cnth=\count283
\Gm@cntv=\count284
\c@Gm@tempcnt=\count285
\Gm@bindingoffset=\dimen151
\Gm@wd@mp=\dimen152
\Gm@odd@mp=\dimen153
\Gm@even@mp=\dimen154
\Gm@layoutwidth=\dimen155
\Gm@layoutheight=\dimen156
\Gm@layouthoffset=\dimen157
\Gm@layoutvoffset=\dimen158
\Gm@dimlist=\toks21

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/geometry/geometry.cfg)) (C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/fancyhdr/fancyhdr.sty
Package: fancyhdr 2025/02/07 v5.2 Extensive control of page headers and footers

\f@nch@headwidth=\skip68
\f@nch@offset@elh=\skip69
\f@nch@offset@erh=\skip70
\f@nch@offset@olh=\skip71
\f@nch@offset@orh=\skip72
\f@nch@offset@elf=\skip73
\f@nch@offset@erf=\skip74
\f@nch@offset@olf=\skip75
\f@nch@offset@orf=\skip76
\f@nch@height=\skip77
\f@nch@footalignment=\skip78
\f@nch@widthL=\skip79
\f@nch@widthC=\skip80
\f@nch@widthR=\skip81
\@temptokenb=\toks22
) (C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/xcolor/xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: luatex.def on input line 274.

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/graphics-def/luatex.def
File: luatex.def 2024/04/13 v1.2e Graphics/color driver for luatex
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1353.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
) (C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/iftex/ifxetex.sty
Package: ifxetex 2019/10/25 v0.7 ifxetex legacy package. Use iftex instead.
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/xifthen/xifthen.sty
Package: xifthen 2015/11/05 v1.4.0 Extended ifthen features

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/tools/calc.sty
Package: calc 2025/03/01 v4.3b Infix arithmetic (KKT,FJ)
\calc@Acount=\count286
\calc@Bcount=\count287
\calc@Adimen=\dimen159
\calc@Bdimen=\dimen160
\calc@Askip=\skip82
\calc@Bskip=\skip83
LaTeX Info: Redefining \setlength on input line 86.
LaTeX Info: Redefining \addtolength on input line 87.
\calc@Ccount=\count288
\calc@Cskip=\skip84
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/base/ifthen.sty
Package: ifthen 2024/03/16 v1.1e Standard LaTeX ifthen package (DPC)
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/ifmtarg/ifmtarg.sty
Package: ifmtarg 2018/04/16 v1.2b check for an empty argument
))
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count289
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/setspace/setspace.sty
Package: setspace 2022/12/04 v6.7b set line spacing
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/fontspec/fontspec.sty
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/l3packages/xparse/xpars
e.sty
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/l3kernel/expl3.sty
Package: expl3 2025-07-20 L3 programming layer (loader) 

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/l3backend/l3backend-lua
tex.def
File: l3backend-luatex.def 2025-06-09 L3 backend support: PDF output (LuaTeX)
\l__color_backend_stack_int=\count290
Inserting `l3color' in `luaotfload.parse_color'.))
Package: xparse 2024-08-16 L3 Experimental document command parser
)
Package: fontspec 2024/05/11 v2.9e Font selection for XeLaTeX and LuaLaTeX
Lua module: fontspec 2024/05/11 v2.9e Font selection for XeLaTeX and LuaLaTeX
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/fontspec/fontspec-luate
x.sty
Package: fontspec-luatex 2024/05/11 v2.9e Font selection for XeLaTeX and LuaLaT
eX
\l__fontspec_script_int=\count291
\l__fontspec_language_int=\count292
\l__fontspec_strnum_int=\count293
\l__fontspec_tmp_int=\count294
\l__fontspec_tmpa_int=\count295
\l__fontspec_tmpb_int=\count296
\l__fontspec_tmpc_int=\count297
\l__fontspec_em_int=\count298
\l__fontspec_emdef_int=\count299
\l__fontspec_strong_int=\count300
\l__fontspec_strongdef_int=\count301
\l__fontspec_tmpa_dim=\dimen161
\l__fontspec_tmpb_dim=\dimen162
\l__fontspec_tmpc_dim=\dimen163
 (C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/base/fontenc.sty
Package: fontenc 2024/12/21 v2.1c Standard LaTeX package
) (C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/fontspec/fontspec.cfg
)))
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/unicode-math/unicode-ma
th.sty
Package: unicode-math 2023/08/13 v0.8r Unicode maths in XeLaTeX and LuaLaTeX

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/unicode-math/unicode-ma
th-luatex.sty
Package: unicode-math-luatex 2023/08/13 v0.8r Unicode maths in XeLaTeX and LuaL
aTeX

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/l3packages/l3keys2e/l3k
eys2e.sty
Package: l3keys2e 2024-08-16 LaTeX2e option processing using LaTeX3 keys
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/base/fix-cm.sty
Package: fix-cm 2020/11/24 v1.1t fixes to LaTeX

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/base/ts1enc.def
File: ts1enc.def 2001/06/05 v3.0e (jk/car/fm) Standard LaTeX file
LaTeX Font Info:    Redeclaring font encoding TS1 on input line 47.
LaTeX Encoding Info:    Redeclaring text command \capitalcedilla (encoding TS1)
 on input line 49.
LaTeX Encoding Info:    Redeclaring text command \capitalogonek (encoding TS1) 
on input line 52.
LaTeX Encoding Info:    Redeclaring text command \capitalgrave (encoding TS1) o
n input line 55.
LaTeX Encoding Info:    Redeclaring text command \capitalacute (encoding TS1) o
n input line 56.
LaTeX Encoding Info:    Redeclaring text command \capitalcircumflex (encoding T
S1) on input line 57.
LaTeX Encoding Info:    Redeclaring text command \capitaltilde (encoding TS1) o
n input line 58.
LaTeX Encoding Info:    Redeclaring text command \capitaldieresis (encoding TS1
) on input line 59.
LaTeX Encoding Info:    Redeclaring text command \capitalhungarumlaut (encoding
 TS1) on input line 60.
LaTeX Encoding Info:    Redeclaring text command \capitalring (encoding TS1) on
 input line 61.
LaTeX Encoding Info:    Redeclaring text command \capitalcaron (encoding TS1) o
n input line 62.
LaTeX Encoding Info:    Redeclaring text command \capitalbreve (encoding TS1) o
n input line 63.
LaTeX Encoding Info:    Redeclaring text command \capitalmacron (encoding TS1) 
on input line 64.
LaTeX Encoding Info:    Redeclaring text command \capitaldotaccent (encoding TS
1) on input line 65.
LaTeX Encoding Info:    Redeclaring text command \t (encoding TS1) on input lin
e 66.
LaTeX Encoding Info:    Redeclaring text command \capitaltie (encoding TS1) on 
input line 67.
LaTeX Encoding Info:    Redeclaring text command \newtie (encoding TS1) on inpu
t line 68.
LaTeX Encoding Info:    Redeclaring text command \capitalnewtie (encoding TS1) 
on input line 69.
LaTeX Encoding Info:    Redeclaring text symbol \textcapitalcompwordmark (encod
ing TS1) on input line 70.
LaTeX Encoding Info:    Redeclaring text symbol \textascendercompwordmark (enco
ding TS1) on input line 71.
LaTeX Encoding Info:    Redeclaring text symbol \textquotestraightbase (encodin
g TS1) on input line 72.
LaTeX Encoding Info:    Redeclaring text symbol \textquotestraightdblbase (enco
ding TS1) on input line 73.
LaTeX Encoding Info:    Redeclaring text symbol \texttwelveudash (encoding TS1)
 on input line 74.
LaTeX Encoding Info:    Redeclaring text symbol \textthreequartersemdash (encod
ing TS1) on input line 75.
LaTeX Encoding Info:    Redeclaring text symbol \textleftarrow (encoding TS1) o
n input line 76.
LaTeX Encoding Info:    Redeclaring text symbol \textrightarrow (encoding TS1) 
on input line 77.
LaTeX Encoding Info:    Redeclaring text symbol \textblank (encoding TS1) on in
put line 78.
LaTeX Encoding Info:    Redeclaring text symbol \textdollar (encoding TS1) on i
nput line 79.
LaTeX Encoding Info:    Redeclaring text symbol \textquotesingle (encoding TS1)
 on input line 80.
LaTeX Encoding Info:    Redeclaring text command \textasteriskcentered (encodin
g TS1) on input line 81.
LaTeX Encoding Info:    Redeclaring text symbol \textdblhyphen (encoding TS1) o
n input line 92.
LaTeX Encoding Info:    Redeclaring text symbol \textfractionsolidus (encoding 
TS1) on input line 93.
LaTeX Encoding Info:    Redeclaring text symbol \textzerooldstyle (encoding TS1
) on input line 94.
LaTeX Encoding Info:    Redeclaring text symbol \textoneoldstyle (encoding TS1)
 on input line 95.
LaTeX Encoding Info:    Redeclaring text symbol \texttwooldstyle (encoding TS1)
 on input line 96.
LaTeX Encoding Info:    Redeclaring text symbol \textthreeoldstyle (encoding TS
1) on input line 97.
LaTeX Encoding Info:    Redeclaring text symbol \textfouroldstyle (encoding TS1
) on input line 98.
LaTeX Encoding Info:    Redeclaring text symbol \textfiveoldstyle (encoding TS1
) on input line 99.
LaTeX Encoding Info:    Redeclaring text symbol \textsixoldstyle (encoding TS1)
 on input line 100.
LaTeX Encoding Info:    Redeclaring text symbol \textsevenoldstyle (encoding TS
1) on input line 101.
LaTeX Encoding Info:    Redeclaring text symbol \texteightoldstyle (encoding TS
1) on input line 102.
LaTeX Encoding Info:    Redeclaring text symbol \textnineoldstyle (encoding TS1
) on input line 103.
LaTeX Encoding Info:    Redeclaring text symbol \textlangle (encoding TS1) on i
nput line 104.
LaTeX Encoding Info:    Redeclaring text symbol \textminus (encoding TS1) on in
put line 105.
LaTeX Encoding Info:    Redeclaring text symbol \textrangle (encoding TS1) on i
nput line 106.
LaTeX Encoding Info:    Redeclaring text symbol \textmho (encoding TS1) on inpu
t line 107.
LaTeX Encoding Info:    Redeclaring text symbol \textbigcircle (encoding TS1) o
n input line 108.
LaTeX Encoding Info:    Redeclaring text command \textcircled (encoding TS1) on
 input line 109.
LaTeX Encoding Info:    Redeclaring text symbol \textohm (encoding TS1) on inpu
t line 115.
LaTeX Encoding Info:    Redeclaring text symbol \textlbrackdbl (encoding TS1) o
n input line 116.
LaTeX Encoding Info:    Redeclaring text symbol \textrbrackdbl (encoding TS1) o
n input line 117.
LaTeX Encoding Info:    Redeclaring text symbol \textuparrow (encoding TS1) on 
input line 118.
LaTeX Encoding Info:    Redeclaring text symbol \textdownarrow (encoding TS1) o
n input line 119.
LaTeX Encoding Info:    Redeclaring text symbol \textasciigrave (encoding TS1) 
on input line 120.
LaTeX Encoding Info:    Redeclaring text symbol \textborn (encoding TS1) on inp
ut line 121.
LaTeX Encoding Info:    Redeclaring text symbol \textdivorced (encoding TS1) on
 input line 122.
LaTeX Encoding Info:    Redeclaring text symbol \textdied (encoding TS1) on inp
ut line 123.
LaTeX Encoding Info:    Redeclaring text symbol \textleaf (encoding TS1) on inp
ut line 124.
LaTeX Encoding Info:    Redeclaring text symbol \textmarried (encoding TS1) on 
input line 125.
LaTeX Encoding Info:    Redeclaring text symbol \textmusicalnote (encoding TS1)
 on input line 126.
LaTeX Encoding Info:    Redeclaring text symbol \texttildelow (encoding TS1) on
 input line 127.
LaTeX Encoding Info:    Redeclaring text symbol \textdblhyphenchar (encoding TS
1) on input line 128.
LaTeX Encoding Info:    Redeclaring text symbol \textasciibreve (encoding TS1) 
on input line 129.
LaTeX Encoding Info:    Redeclaring text symbol \textasciicaron (encoding TS1) 
on input line 130.
LaTeX Encoding Info:    Redeclaring text symbol \textacutedbl (encoding TS1) on
 input line 131.
LaTeX Encoding Info:    Redeclaring text symbol \textgravedbl (encoding TS1) on
 input line 132.
LaTeX Encoding Info:    Redeclaring text symbol \textdagger (encoding TS1) on i
nput line 133.
LaTeX Encoding Info:    Redeclaring text symbol \textdaggerdbl (encoding TS1) o
n input line 134.
LaTeX Encoding Info:    Redeclaring text symbol \textbardbl (encoding TS1) on i
nput line 135.
LaTeX Encoding Info:    Redeclaring text symbol \textperthousand (encoding TS1)
 on input line 136.
LaTeX Encoding Info:    Redeclaring text symbol \textbullet (encoding TS1) on i
nput line 137.
LaTeX Encoding Info:    Redeclaring text symbol \textcelsius (encoding TS1) on 
input line 138.
LaTeX Encoding Info:    Redeclaring text symbol \textdollaroldstyle (encoding T
S1) on input line 139.
LaTeX Encoding Info:    Redeclaring text symbol \textcentoldstyle (encoding TS1
) on input line 140.
LaTeX Encoding Info:    Redeclaring text symbol \textflorin (encoding TS1) on i
nput line 141.
LaTeX Encoding Info:    Redeclaring text symbol \textcolonmonetary (encoding TS
1) on input line 142.
LaTeX Encoding Info:    Redeclaring text symbol \textwon (encoding TS1) on inpu
t line 143.
LaTeX Encoding Info:    Redeclaring text symbol \textnaira (encoding TS1) on in
put line 144.
LaTeX Encoding Info:    Redeclaring text symbol \textguarani (encoding TS1) on 
input line 145.
LaTeX Encoding Info:    Redeclaring text symbol \textpeso (encoding TS1) on inp
ut line 146.
LaTeX Encoding Info:    Redeclaring text symbol \textlira (encoding TS1) on inp
ut line 147.
LaTeX Encoding Info:    Redeclaring text symbol \textrecipe (encoding TS1) on i
nput line 148.
LaTeX Encoding Info:    Redeclaring text symbol \textinterrobang (encoding TS1)
 on input line 149.
LaTeX Encoding Info:    Redeclaring text symbol \textinterrobangdown (encoding 
TS1) on input line 150.
LaTeX Encoding Info:    Redeclaring text symbol \textdong (encoding TS1) on inp
ut line 151.
LaTeX Encoding Info:    Redeclaring text symbol \texttrademark (encoding TS1) o
n input line 152.
LaTeX Encoding Info:    Redeclaring text symbol \textpertenthousand (encoding T
S1) on input line 153.
LaTeX Encoding Info:    Redeclaring text symbol \textpilcrow (encoding TS1) on 
input line 154.
LaTeX Encoding Info:    Redeclaring text symbol \textbaht (encoding TS1) on inp
ut line 155.
LaTeX Encoding Info:    Redeclaring text symbol \textnumero (encoding TS1) on i
nput line 156.
LaTeX Encoding Info:    Redeclaring text symbol \textdiscount (encoding TS1) on
 input line 157.
LaTeX Encoding Info:    Redeclaring text symbol \textestimated (encoding TS1) o
n input line 158.
LaTeX Encoding Info:    Redeclaring text symbol \textopenbullet (encoding TS1) 
on input line 159.
LaTeX Encoding Info:    Redeclaring text symbol \textservicemark (encoding TS1)
 on input line 160.
LaTeX Encoding Info:    Redeclaring text symbol \textlquill (encoding TS1) on i
nput line 161.
LaTeX Encoding Info:    Redeclaring text symbol \textrquill (encoding TS1) on i
nput line 162.
LaTeX Encoding Info:    Redeclaring text symbol \textcent (encoding TS1) on inp
ut line 163.
LaTeX Encoding Info:    Redeclaring text symbol \textsterling (encoding TS1) on
 input line 164.
LaTeX Encoding Info:    Redeclaring text symbol \textcurrency (encoding TS1) on
 input line 165.
LaTeX Encoding Info:    Redeclaring text symbol \textyen (encoding TS1) on inpu
t line 166.
LaTeX Encoding Info:    Redeclaring text symbol \textbrokenbar (encoding TS1) o
n input line 167.
LaTeX Encoding Info:    Redeclaring text symbol \textsection (encoding TS1) on 
input line 168.
LaTeX Encoding Info:    Redeclaring text symbol \textasciidieresis (encoding TS
1) on input line 169.
LaTeX Encoding Info:    Redeclaring text symbol \textcopyright (encoding TS1) o
n input line 170.
LaTeX Encoding Info:    Redeclaring text symbol \textordfeminine (encoding TS1)
 on input line 171.
LaTeX Encoding Info:    Redeclaring text symbol \textcopyleft (encoding TS1) on
 input line 172.
LaTeX Encoding Info:    Redeclaring text symbol \textlnot (encoding TS1) on inp
ut line 173.
LaTeX Encoding Info:    Redeclaring text symbol \textcircledP (encoding TS1) on
 input line 174.
LaTeX Encoding Info:    Redeclaring text symbol \textregistered (encoding TS1) 
on input line 175.
LaTeX Encoding Info:    Redeclaring text symbol \textasciimacron (encoding TS1)
 on input line 176.
LaTeX Encoding Info:    Redeclaring text symbol \textdegree (encoding TS1) on i
nput line 177.
LaTeX Encoding Info:    Redeclaring text symbol \textpm (encoding TS1) on input
 line 178.
LaTeX Encoding Info:    Redeclaring text symbol \texttwosuperior (encoding TS1)
 on input line 179.
LaTeX Encoding Info:    Redeclaring text symbol \textthreesuperior (encoding TS
1) on input line 180.
LaTeX Encoding Info:    Redeclaring text symbol \textasciiacute (encoding TS1) 
on input line 181.
LaTeX Encoding Info:    Redeclaring text symbol \textmu (encoding TS1) on input
 line 182.
LaTeX Encoding Info:    Redeclaring text symbol \textparagraph (encoding TS1) o
n input line 183.
LaTeX Encoding Info:    Redeclaring text symbol \textperiodcentered (encoding T
S1) on input line 184.
LaTeX Encoding Info:    Redeclaring text symbol \textreferencemark (encoding TS
1) on input line 185.
LaTeX Encoding Info:    Redeclaring text symbol \textonesuperior (encoding TS1)
 on input line 186.
LaTeX Encoding Info:    Redeclaring text symbol \textordmasculine (encoding TS1
) on input line 187.
LaTeX Encoding Info:    Redeclaring text symbol \textsurd (encoding TS1) on inp
ut line 188.
LaTeX Encoding Info:    Redeclaring text symbol \textonequarter (encoding TS1) 
on input line 189.
LaTeX Encoding Info:    Redeclaring text symbol \textonehalf (encoding TS1) on 
input line 190.
LaTeX Encoding Info:    Redeclaring text symbol \textthreequarters (encoding TS
1) on input line 191.
LaTeX Encoding Info:    Redeclaring text symbol \texteuro (encoding TS1) on inp
ut line 192.
LaTeX Encoding Info:    Redeclaring text symbol \texttimes (encoding TS1) on in
put line 193.
LaTeX Encoding Info:    Redeclaring text symbol \textdiv (encoding TS1) on inpu
t line 194.
))
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/amsmath/amsmath.sty
Package: amsmath 2025/06/16 v2.17y AMS math features
\@mathmargin=\skip85

For additional information on amsmath, use the `?' option.
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/amsmath/amstext.sty
Package: amstext 2024/11/17 v2.01 AMS text

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks23
\ex@=\dimen164
))
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen165
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count302
LaTeX Info: Redefining \frac on input line 235.
\uproot@=\count303
\leftroot@=\count304
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count305
\DOTSCASE@=\count306
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box56
\strutbox@=\box57
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen166
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count307
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count308
\dotsspace@=\muskip17
\c@parentequation=\count309
\dspbrk@lvl=\count310
\tag@help=\toks24
\row@=\count311
\column@=\count312
\maxfields@=\count313
\andhelp@=\toks25
\eqnshift@=\dimen167
\alignsep@=\dimen168
\tagshift@=\dimen169
\tagwidth@=\dimen170
\totwidth@=\dimen171
\lineht@=\dimen172
\@envbody=\toks26
\multlinegap=\skip86
\multlinetaggap=\skip87
\mathdisplay@stack=\toks27
LaTeX Info: Redefining \[ on input line 2949.
LaTeX Info: Redefining \] on input line 2950.
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/lualatex/lualatex-math/lualat
ex-math.sty
Package: lualatex-math 2022/01/01 v1.12 Patches for mathematics typesetting wit
h LuaLaTeX
Lua module: lualatex-math 2013/08/03 v1.3 Patches for mathematics typesetting wi
th LuaLaTeX)
\g__um_fam_int=\count314
\g__um_fonts_used_int=\count315
\l__um_primecount_int=\count316
\g__um_primekern_muskip=\muskip18

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/unicode-math/unicode-ma
th-table.tex)))
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/fontawesome5/fontawesom
e5.sty
Package: fontawesome5 2022/05/02 v5.15.4 Font Awesome 5

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/fontawesome5/fontawesom
e5-utex-helper.sty
Package: fontawesome5-utex-helper 2022/05/02 v5.15.4 uTeX helper for fontawesom
e5

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/luatex/luatexbase/luatexbase.
sty
Package: luatexbase 2015/10/04 v1.3 luatexbase interface to LuaTeX 

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/luatex/ctablestack/ctablestac
k.sty
Package: ctablestack 2015/10/01 v1.0 Catcode table stable support
\@catcodetablestackcnt=\count317
)
\CatcodeTableOther=\catcodetable14
\CatcodeTableExpl=\catcodetable15
)
LaTeX Font Info:    Trying to load font information for TU+fontawesomefree on i
nput line 69.

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/fontawesome5/tufontawes
omefree.fd)
LaTeX Font Info:    Trying to load font information for TU+fontawesomebrands on
 input line 70.

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/fontawesome5/tufontawes
omebrands.fd)))
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/roboto/roboto.sty
Package: roboto 2022/09/10 (Bob Tennent) Supports Roboto fonts for all LaTeX en
gines.

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/iftex/ifluatex.sty
Package: ifluatex 2019/10/25 v1.5 ifluatex legacy package. Use iftex instead.
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/xkeyval/xkeyval.sty
Package: xkeyval 2022/06/16 v2.9 package option processing (HA)

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/xkeyval/xkeyval.tex
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/xkeyval/xkvutils.tex
\XKV@toks=\toks28
\XKV@tempa@toks=\toks29
)
\XKV@depth=\count318
File: xkeyval.tex 2014/12/03 v2.7a key=value parser (HA)
)) (C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/base/textcomp.sty
Package: textcomp 2024/04/24 v2.1b Standard LaTeX package
)
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> TU/Roboto(1)/m/n on input line 150.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> TU/Roboto(1)/bx/n on input line 150.
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/sourcesanspro/sourcesan
spro.sty
Package: sourcesanspro 2018/05/19 v2.7 Adobe's Source Sans Pro typeface
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/tcolorbox/tcolorbox.sty
Package: tcolorbox 2025/07/08 version 6.7.1 text color boxes

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/pgf/frontendlayer/tikz.
sty
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/pgf/basiclayer/pgf.sty
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/pgf/utilities/pgfrcs.st
y
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/utilities/pgfutil
-common.tex
\pgfutil@everybye=\toks30
\pgfutil@tempdima=\dimen173
\pgfutil@tempdimb=\dimen174
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/utilities/pgfutil
-latex.def
\pgfutil@abb=\box58
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/utilities/pgfrcs.
code.tex
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
Package: pgf 2023-01-15 v3.1.10 (3.1.10)

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/pgf/basiclayer/pgfcore.
sty
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/graphics/graphicx.sty
Package: graphicx 2024/12/31 v1.2e Enhanced LaTeX Graphics (DPC,SPQR)

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/graphics-cfg/graphics.c
fg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: luatex.def on input line 106.
)
\Gin@req@height=\dimen175
\Gin@req@width=\dimen176
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/pgf/systemlayer/pgfsys.
sty
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/systemlayer/pgfsy
s.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/utilities/pgfkeys
.code.tex
\pgfkeys@pathtoks=\toks31
\pgfkeys@temptoks=\toks32

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/utilities/pgfkeys
libraryfiltered.code.tex
\pgfkeys@tmptoks=\toks33
))
\pgf@x=\dimen177
\pgf@y=\dimen178
\pgf@xa=\dimen179
\pgf@ya=\dimen180
\pgf@xb=\dimen181
\pgf@yb=\dimen182
\pgf@xc=\dimen183
\pgf@yc=\dimen184
\pgf@xd=\dimen185
\pgf@yd=\dimen186
\w@pgf@writea=\write3
\r@pgf@reada=\read2
\c@pgf@counta=\count319
\c@pgf@countb=\count320
\c@pgf@countc=\count321
\c@pgf@countd=\count322
\t@pgf@toka=\toks34
\t@pgf@tokb=\toks35
\t@pgf@tokc=\toks36
\pgf@sys@id@count=\count323

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/systemlayer/pgf.c
fg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-luatex.def

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/systemlayer/pgfsy
s-luatex.def
File: pgfsys-luatex.def 2023-01-15 v3.1.10 (3.1.10)

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/systemlayer/pgfsy
s-common-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
)))
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/systemlayer/pgfsy
ssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count324
\pgfsyssoftpath@bigbuffer@items=\count325
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/systemlayer/pgfsy
sprotocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/basiclayer/pgfcor
e.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/math/pgfmath.code
.tex
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/math/pgfmathutil.
code.tex)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/math/pgfmathparse
r.code.tex
\pgfmath@dimen=\dimen187
\pgfmath@count=\count326
\pgfmath@box=\box59
\pgfmath@toks=\toks37
\pgfmath@stack@operand=\toks38
\pgfmath@stack@operation=\toks39
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/math/pgfmathfunct
ions.code.tex)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/math/pgfmathfunct
ions.basic.code.tex)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/math/pgfmathfunct
ions.trigonometric.code.tex)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/math/pgfmathfunct
ions.random.code.tex)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/math/pgfmathfunct
ions.comparison.code.tex)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/math/pgfmathfunct
ions.base.code.tex)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/math/pgfmathfunct
ions.round.code.tex)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/math/pgfmathfunct
ions.misc.code.tex)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/math/pgfmathfunct
ions.integerarithmetics.code.tex)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/math/pgfmathcalc.
code.tex)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/math/pgfmathfloat
.code.tex
\c@pgfmathroundto@lastzeros=\count327
))
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/math/pgfint.code.
tex)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/basiclayer/pgfcor
epoints.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen188
\pgf@picmaxx=\dimen189
\pgf@picminy=\dimen190
\pgf@picmaxy=\dimen191
\pgf@pathminx=\dimen192
\pgf@pathmaxx=\dimen193
\pgf@pathminy=\dimen194
\pgf@pathmaxy=\dimen195
\pgf@xx=\dimen196
\pgf@xy=\dimen197
\pgf@yx=\dimen198
\pgf@yy=\dimen199
\pgf@zx=\dimen256
\pgf@zy=\dimen257
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/basiclayer/pgfcor
epathconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen258
\pgf@path@lasty=\dimen259
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/basiclayer/pgfcor
epathusage.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen260
\pgf@shorten@start@additional=\dimen261
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/basiclayer/pgfcor
escopes.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box60
\pgf@hbox=\box61
\pgf@layerbox@main=\box62
\pgf@picture@serial@count=\count328
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/basiclayer/pgfcor
egraphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen262
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/basiclayer/pgfcor
etransformations.code.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen263
\pgf@pt@y=\dimen264
\pgf@pt@temp=\dimen265
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/basiclayer/pgfcor
equick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/basiclayer/pgfcor
eobjects.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/basiclayer/pgfcor
epathprocessing.code.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/basiclayer/pgfcor
earrows.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen266
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/basiclayer/pgfcor
eshade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen267
\pgf@sys@shading@range@num=\count329
\pgf@shadingcount=\count330
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/basiclayer/pgfcor
eimage.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/basiclayer/pgfcor
eexternal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box63
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/basiclayer/pgfcor
elayers.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/basiclayer/pgfcor
etransparency.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/basiclayer/pgfcor
epatterns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/basiclayer/pgfcor
erdf.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/modules/pgfmodule
shapes.code.tex
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box64
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/modules/pgfmodule
plot.code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/pgf/compatibility/pgfco
mp-version-0-65.sty
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen268
\pgf@nodesepend=\dimen269
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/pgf/compatibility/pgfco
mp-version-1-18.sty
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
))
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/pgf/utilities/pgffor.st
y
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/pgf/utilities/pgfkeys.s
ty
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/utilities/pgfkeys
.code.tex))
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/pgf/math/pgfmath.sty
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/math/pgfmath.code
.tex))
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/utilities/pgffor.
code.tex
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen270
\pgffor@skip=\dimen271
\pgffor@stack=\toks40
\pgffor@toks=\toks41
))
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/frontendlayer/tik
z/tikz.code.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/libraries/pgflibr
aryplothandlers.code.tex
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count331
\pgfplotmarksize=\dimen272
)
\tikz@lastx=\dimen273
\tikz@lasty=\dimen274
\tikz@lastxsaved=\dimen275
\tikz@lastysaved=\dimen276
\tikz@lastmovetox=\dimen277
\tikz@lastmovetoy=\dimen278
\tikzleveldistance=\dimen279
\tikzsiblingdistance=\dimen280
\tikz@figbox=\box65
\tikz@figbox@bg=\box66
\tikz@tempbox=\box67
\tikz@tempbox@bg=\box68
\tikztreelevel=\count332
\tikznumberofchildren=\count333
\tikznumberofcurrentchild=\count334
\tikz@fig@count=\count335

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/modules/pgfmodule
matrix.code.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count336
\pgfmatrixcurrentcolumn=\count337
\pgf@matrix@numberofcolumns=\count338
)
\tikz@expandcount=\count339

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pgf/frontendlayer/tik
z/libraries/tikzlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/tools/verbatim.sty
Package: verbatim 2024-01-22 v1.5x LaTeX2e package for verbatim enhancements
\every@verbatim=\toks42
\verbatim@line=\toks43
\verbatim@in@stream=\read3
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/environ/environ.sty
Package: environ 2014/05/04 v0.3 A new way to define environments

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/trimspaces/trimspaces.s
ty
Package: trimspaces 2009/09/17 v1.1 Trim spaces around a token list
))
\tcb@titlebox=\box69
\tcb@upperbox=\box70
\tcb@lowerbox=\box71
\tcb@phantombox=\box72
\c@tcbbreakpart=\count340
\c@tcblayer=\count341
\c@tcolorbox@number=\count342
\l__tcobox_tmpa_box=\box73
\l__tcobox_tmpa_dim=\dimen281
\tcb@temp=\box74
\tcb@temp=\box75
\tcb@temp=\box76
\tcb@temp=\box77

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/tcolorbox/tcbskins.code
.tex
Library (tcolorbox): 'tcbskins.code.tex' version '6.7.1'

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/tikzfill/tikzfill.image
.sty
Package: tikzfill.image 2023/08/08 v1.0.1 Image filling library for TikZ

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/tikzfill/tikzfill-commo
n.sty
Package: tikzfill-common 2023/08/08 v1.0.1 Auxiliary code for tikzfill
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/tikzfill/tikzlibraryfil
l.image.code.tex
File: tikzlibraryfill.image.code.tex 2023/08/08 v1.0.1 Image filling library
\l__tikzfill_img_box=\box78
))
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/tcolorbox/tcbskinsjigsa
w.code.tex
Library (tcolorbox): 'tcbskinsjigsaw.code.tex' version '6.7.1'
))) (C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/parskip/parskip.sty
Package: parskip 2021-03-14 v2.0h non-zero parskip adjustments

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
)))
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/hyperref/hyperref.sty
Package: hyperref 2025-07-12 v7.01o Hypertext links for LaTeX

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/kvdefinekeys/kvdefine
keys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pdfescape/pdfescape.s
ty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/pdftexcmds/pdftexcmds
.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/infwarerr/infwarerr.s
ty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
\pdftexcmds@toks=\toks44
))
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/hyperref/nameref.sty
Package: nameref 2025-06-21 v2.57 Cross-referencing by name of section

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/gettitlestring/gettit
lestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
)
\c@section@level=\count343
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/stringenc/stringenc.s
ty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO
)
)
\@linkdim=\dimen282
\Hy@linkcounter=\count344
\Hy@pagecounter=\count345
 (C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2025-07-12 v7.01o Hyperref: PDFDocEncoding definition (HO)
) (C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count346
 (C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/hyperref/puenc.def
File: puenc.def 2025-07-12 v7.01o Hyperref: PDF Unicode definition (HO)
)
Package hyperref Info: Option `unicode' set `true' on input line 4066.
Package hyperref Info: Option `pdfpagelabels' set `false' on input line 4066.
Package hyperref Info: Hyper figures OFF on input line 4195.
Package hyperref Info: Link nesting OFF on input line 4200.
Package hyperref Info: Hyper index ON on input line 4203.
Package hyperref Info: Plain pages OFF on input line 4210.
Package hyperref Info: Backreferencing OFF on input line 4215.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4462.
\c@Hy@tempcnt=\count347

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/url/url.sty
\Urlmuskip=\muskip19
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4801.
\XeTeXLinkMargin=\dimen283

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/bigintcalc/bigintcalc
.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO
)
))
\Fld@menulength=\count348
\Field@Width=\dimen284
\Fld@charsize=\dimen285
Package hyperref Info: Hyper figures OFF on input line 6078.
Package hyperref Info: Link nesting OFF on input line 6083.
Package hyperref Info: Hyper index ON on input line 6086.
Package hyperref Info: backreferencing OFF on input line 6093.
Package hyperref Info: Link coloring OFF on input line 6098.
Package hyperref Info: Link coloring with OCG OFF on input line 6103.
Package hyperref Info: PDF/A mode OFF on input line 6108.
\c@Item=\count349
\c@Hfootnote=\count350
)
Package hyperref Info: Driver (autodetected): hluatex.

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/hyperref/hluatex.def
File: hluatex.def 2025-07-12 v7.01o Hyperref driver for luaTeX
\Fld@listcount=\count351
\c@bookmark@seq@number=\count352

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/rerunfilecheck/rerunfil
echeck.sty
Package: rerunfilecheck 2025-06-21 v1.11 Rerun checks for auxiliary files (HO)

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/generic/uniquecounter/uniquec
ounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 2
84.
)
\Hy@SectionHShift=\skip88
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/bookmark/bookmark.sty
Package: bookmark 2023-12-10 v1.31 PDF bookmarks (HO)

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/bookmark/bkm-pdftex.def
File: bkm-pdftex.def 2023-12-10 v1.31 bookmark driver for pdfTeX and luaTeX (HO
)
\BKM@id=\count353
))
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/biblatex/biblatex.sty
Package: biblatex 2025/07/10 v3.21 programmable bibliographies (PK/MW)

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/logreq/logreq.sty
Package: logreq 2010/08/04 v1.0 xml request logger
\lrq@indent=\count354

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/logreq/logreq.def
File: logreq.def 2010/08/04 v1.0 logreq spec v1.0
))
\c@tabx@nest=\count355
\c@listtotal=\count356
\c@listcount=\count357
\c@liststart=\count358
\c@liststop=\count359
\c@citecount=\count360
\c@citetotal=\count361
\c@multicitecount=\count362
\c@multicitetotal=\count363
\c@instcount=\count364
\c@maxnames=\count365
\c@minnames=\count366
\c@maxitems=\count367
\c@minitems=\count368
\c@citecounter=\count369
\c@maxcitecounter=\count370
\c@savedcitecounter=\count371
\c@uniquelist=\count372
\c@uniquename=\count373
\c@refsection=\count374
\c@refsegment=\count375
\c@maxextratitle=\count376
\c@maxextratitleyear=\count377
\c@maxextraname=\count378
\c@maxextradate=\count379
\c@maxextraalpha=\count380
\c@abbrvpenalty=\count381
\c@highnamepenalty=\count382
\c@lownamepenalty=\count383
\c@maxparens=\count384
\c@parenlevel=\count385
\blx@tempcnta=\count386
\blx@tempcntb=\count387
\blx@tempcntc=\count388
\c@blx@maxsection=\count389
\blx@maxsegment@0=\count390
\blx@notetype=\count391
\blx@parenlevel@text=\count392
\blx@parenlevel@foot=\count393
\blx@sectionciteorder@0=\count394
\blx@sectionciteorderinternal@0=\count395
\blx@entrysetcounter=\count396
\blx@biblioinstance=\count397
\labelnumberwidth=\skip89
\labelalphawidth=\skip90
\biblabelsep=\skip91
\bibitemsep=\skip92
\bibnamesep=\skip93
\bibinitsep=\skip94
\bibparsep=\skip95
\bibhang=\skip96
\blx@bcfin=\read4
\blx@bcfout=\write4
\blx@langwohyphens=\language2
\c@mincomprange=\count398
\c@maxcomprange=\count399
\c@mincompwidth=\count400
Package biblatex Info: Trying to load biblatex default data model...
Package biblatex Info: ... file 'blx-dm.def' found.

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/biblatex/blx-dm.def
File: blx-dm.def 2025/07/10 v3.21 biblatex datamodel (PK/MW)
)
Package biblatex Info: Trying to load biblatex style data model...
Package biblatex Info: ... file 'verbose.dbx' not found.
Package biblatex Info: Trying to load biblatex custom data model...
Package biblatex Info: ... file 'biblatex-dm.cfg' not found.
\c@afterword=\count401
\c@savedafterword=\count402
\c@annotator=\count403
\c@savedannotator=\count404
\c@author=\count405
\c@savedauthor=\count406
\c@bookauthor=\count407
\c@savedbookauthor=\count408
\c@commentator=\count409
\c@savedcommentator=\count410
\c@editor=\count411
\c@savededitor=\count412
\c@editora=\count413
\c@savededitora=\count414
\c@editorb=\count415
\c@savededitorb=\count416
\c@editorc=\count417
\c@savededitorc=\count418
\c@foreword=\count419
\c@savedforeword=\count420
\c@holder=\count421
\c@savedholder=\count422
\c@introduction=\count423
\c@savedintroduction=\count424
\c@namea=\count425
\c@savednamea=\count426
\c@nameb=\count427
\c@savednameb=\count428
\c@namec=\count429
\c@savednamec=\count430
\c@translator=\count431
\c@savedtranslator=\count432
\c@shortauthor=\count433
\c@savedshortauthor=\count434
\c@shorteditor=\count435
\c@savedshorteditor=\count436
\c@labelname=\count437
\c@savedlabelname=\count438
\c@institution=\count439
\c@savedinstitution=\count440
\c@lista=\count441
\c@savedlista=\count442
\c@listb=\count443
\c@savedlistb=\count444
\c@listc=\count445
\c@savedlistc=\count446
\c@listd=\count447
\c@savedlistd=\count448
\c@liste=\count449
\c@savedliste=\count450
\c@listf=\count451
\c@savedlistf=\count452
\c@location=\count453
\c@savedlocation=\count454
\c@organization=\count455
\c@savedorganization=\count456
\c@origlocation=\count457
\c@savedoriglocation=\count458
\c@origpublisher=\count459
\c@savedorigpublisher=\count460
\c@publisher=\count461
\c@savedpublisher=\count462
\c@language=\count463
\c@savedlanguage=\count464
\c@origlanguage=\count465
\c@savedoriglanguage=\count466
\c@pageref=\count467
\c@savedpageref=\count468
\shorthandwidth=\skip97
\shortjournalwidth=\skip98
\shortserieswidth=\skip99
\shorttitlewidth=\skip100
\shortauthorwidth=\skip101
\shorteditorwidth=\skip102
\locallabelnumberwidth=\skip103
\locallabelalphawidth=\skip104
\localshorthandwidth=\skip105
\localshortjournalwidth=\skip106
\localshortserieswidth=\skip107
\localshorttitlewidth=\skip108
\localshortauthorwidth=\skip109
\localshorteditorwidth=\skip110
Package biblatex Info: Trying to load enhanced support for Unicode engines...
Package biblatex Info: ... file 'blx-unicode.def' found.

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/biblatex/blx-unicode.de
f)
Package biblatex Info: Trying to load compatibility code...
Package biblatex Info: ... file 'blx-compat.def' found.

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/biblatex/blx-compat.def
File: blx-compat.def 2025/07/10 v3.21 biblatex compatibility (PK/MW)
)
Package biblatex Info: Trying to load generic definitions...
Package biblatex Info: ... file 'biblatex.def' found.
 (C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/biblatex/biblatex.def
File: biblatex.def 2025/07/10 v3.21 biblatex compatibility (PK/MW)
\c@textcitecount=\count469
\c@textcitetotal=\count470
\c@textcitemaxnames=\count471
\c@biburlbigbreakpenalty=\count472
\c@biburlbreakpenalty=\count473
\c@biburlnumpenalty=\count474
\c@biburlucpenalty=\count475
\c@biburllcpenalty=\count476
\biburlbigskip=\muskip20
\biburlnumskip=\muskip21
\biburlucskip=\muskip22
\biburllcskip=\muskip23
\c@smartand=\count477
)
Package biblatex Info: Trying to load bibliography style 'verbose'...
Package biblatex Info: ... file 'verbose.bbx' found.

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/biblatex/bbx/verbose.bb
x
File: verbose.bbx 2025/07/10 v3.21 biblatex bibliography style (PK/MW)
Package biblatex Info: Trying to load bibliography style 'authortitle'...
Package biblatex Info: ... file 'authortitle.bbx' found.

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/biblatex/bbx/authortitl
e.bbx
File: authortitle.bbx 2025/07/10 v3.21 biblatex bibliography style (PK/MW)
Package biblatex Info: Trying to load bibliography style 'standard'...
Package biblatex Info: ... file 'standard.bbx' found.

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/biblatex/bbx/standard.b
bx
File: standard.bbx 2025/07/10 v3.21 biblatex bibliography style (PK/MW)
\c@bbx:relatedcount=\count478
\c@bbx:relatedtotal=\count479
)))
Package biblatex Info: Trying to load citation style 'verbose'...
Package biblatex Info: ... file 'verbose.cbx' found.

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/biblatex/cbx/verbose.cb
x
File: verbose.cbx 2025/07/10 v3.21 biblatex citation style (PK/MW)
Package biblatex Info: Redefining '\cite'.
Package biblatex Info: Redefining '\parencite'.
Package biblatex Info: Redefining '\footcite'.
Package biblatex Info: Redefining '\footcitetext'.
Package biblatex Info: Redefining '\smartcite'.
Package biblatex Info: Redefining '\textcite'.
Package biblatex Info: Redefining '\textcites'.
)
Package biblatex Info: Trying to load configuration file...
Package biblatex Info: ... file 'biblatex.cfg' found.

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/biblatex/biblatex.cfg
File: biblatex.cfg 
)
Package biblatex Info: LuaTeX detected.
(biblatex)             Assuming input encoding 'utf8'.
Package biblatex Info: Document encoding is UTF8 ....
Package biblatex Info: ... and expl3
(biblatex)             2025-07-20 L3 programming layer (loader) 
(biblatex)             is new enough (at least 2020/04/06),
(biblatex)             setting 'casechanger=expl3'.

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/biblatex/blx-case-expl3
.sty
Package: blx-case-expl3 2025/07/10 v3.21 expl3 case changing code for biblatex
)))
\@quotelevel=\count480
\@quotereset=\count481
LaTeX Font Info:    Font shape `TU/SourceSansPro(0)/m/n' will be
(Font)              scaled to size 10.95pt on input line 71.
 (./resume.aux)
\openout1 = resume.aux

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 71.
LaTeX Font Info:    ... okay on input line 71.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 71.
LaTeX Font Info:    ... okay on input line 71.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 71.
LaTeX Font Info:    ... okay on input line 71.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 71.
LaTeX Font Info:    ... okay on input line 71.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 71.
LaTeX Font Info:    ... okay on input line 71.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 71.
LaTeX Font Info:    ... okay on input line 71.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 71.
LaTeX Font Info:    ... okay on input line 71.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 71.
LaTeX Font Info:    ... okay on input line 71.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 71.
LaTeX Font Info:    ... okay on input line 71.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 71.
LaTeX Font Info:    ... okay on input line 71.

*geometry* driver: auto-detecting
*geometry* detected driver: luatex
*geometry* verbose mode - [ preamble ] result:
* driver: luatex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(39.83368pt, 517.84052pt, 39.83368pt)
* v-part:(T,H,B)=(22.76228pt, 771.06952pt, 51.21504pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=517.84052pt
* \textheight=771.06952pt
* \oddsidemargin=-32.43631pt
* \evensidemargin=-32.43631pt
* \topmargin=-86.5077pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=11.0pt
* \footskip=14.22636pt
* \marginparwidth=50.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)


(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/context/base/mkii/supp-pdf.mk
ii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count482
\scratchdimen=\dimen286
\scratchbox=\box79
\nofMPsegments=\count483
\nofMParguments=\count484
\everyMPshowfont=\toks45
\MPscratchCnt=\count485
\MPscratchDim=\dimen287
\MPnumerator=\count486
\makeMPintoPDFobject=\count487
\everyMPtoPDFconversion=\toks46
)
(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/epstopdf-pkg/epstopdf-b
ase.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/00miktex/epstopdf-sys.c
fg
File: epstopdf-sys.cfg 2021/03/18 v2.0 Configuration of epstopdf for MiKTeX
))
LaTeX Font Info:    Overwriting math alphabet `\mathrm' in version `normal'
(Font)                  OT1/cmr/m/n --> TU/lmr/m/n on input line 71.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> TU/lmr/m/it on input line 71.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> TU/lmr/bx/n on input line 71.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/m/n on input line 71.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/bx/n on input line 71.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `normal' on input line 71.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> TU/latinmodern-math.otf(1)/m/n on input
 line 71.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `bold' on input line 71.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> TU/latinmodern-math.otf(1)/b/n on inpu
t line 71.
LaTeX Font Info:    Encoding `OMS' has changed to `TU' for symbol font
(Font)              `symbols' in the math version `normal' on input line 71.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `normal'
(Font)                  OMS/cmsy/m/n --> TU/latinmodern-math.otf(2)/m/n on inpu
t line 71.
LaTeX Font Info:    Encoding `OMS' has changed to `TU' for symbol font
(Font)              `symbols' in the math version `bold' on input line 71.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  OMS/cmsy/b/n --> TU/latinmodern-math.otf(2)/b/n on inpu
t line 71.
LaTeX Font Info:    Encoding `OMX' has changed to `TU' for symbol font
(Font)              `largesymbols' in the math version `normal' on input line 7
1.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `normal'
(Font)                  OMX/cmex/m/n --> TU/latinmodern-math.otf(3)/m/n on inpu
t line 71.
LaTeX Font Info:    Encoding `OMX' has changed to `TU' for symbol font
(Font)              `largesymbols' in the math version `bold' on input line 71.

LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  OMX/cmex/m/n --> TU/latinmodern-math.otf(3)/b/n on inpu
t line 71.
Package hyperref Info: Link coloring OFF on input line 71.
Package biblatex Info: Trying to load language 'english'...
Package biblatex Info: ... file 'english.lbx' found.

(C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/biblatex/lbx/english.lb
x
File: english.lbx 2025/07/10 v3.21 biblatex localization (PK/MW)
)
Package biblatex Info: LuaTeX detected.
(biblatex)             Assuming input encoding 'utf8'.
Package biblatex Info: Automatic encoding selection.
(biblatex)             Assuming data encoding 'utf8'.

\openout4 = resume.bcf
Package biblatex Info: Trying to load bibliographic data...
Package biblatex Info: ... file 'resume.bbl' found.
 (./resume.bbl)
Package biblatex Info: Reference section=0 on input line 71.
Package biblatex Info: Reference segment=0 on input line 71.
\headertextwidth=\skip111
\headerphotowidth=\skip112
LaTeX Font Info:    Font shape `TU/Roboto(2)/m/n' will be
(Font)              scaled to size 20.0pt on input line 76.
LaTeX Font Info:    Font shape `TU/SourceSansPro(1)/m/n' will be
(Font)              scaled to size 7.6pt on input line 76.
LaTeX Font Info:    Font shape `TU/SourceSansPro(1)/m/sc' will be
(Font)              scaled to size 7.6pt on input line 76.
LaTeX Font Info:    Font shape `TU/Roboto(2)/m/n' will be
(Font)              scaled to size 8.0pt on input line 76.
LaTeX Font Info:    Font shape `TU/Roboto(2)/m/it' will be
(Font)              scaled to size 8.0pt on input line 76.
LaTeX Font Info:    Font shape `TU/Roboto(2)/m/n' will be
(Font)              scaled to size 6.8pt on input line 76.

Overfull \hbox (1.46pt too wide) in paragraph at lines 76--76
[]$[]$ $[]$$[]$ 
 []

(./cv/summary.tex
LaTeX Font Info:    Font shape `TU/SourceSansPro(1)/m/n' will be
(Font)              scaled to size 16.0pt on input line 4.
LaTeX Font Info:    Font shape `TU/SourceSansPro(1)/b/n' will be
(Font)              scaled to size 16.0pt on input line 4.
LaTeX Font Info:    Font shape `TU/SourceSansPro(2)/m/n' will be
(Font)              scaled to size 9.0pt on input line 10.
) (./cv/education.tex
LaTeX Font Info:    Font shape `TU/SourceSansPro(1)/m/n' will be
(Font)              scaled to size 10.0pt on input line 23.
LaTeX Font Info:    Font shape `TU/SourceSansPro(1)/b/n' will be
(Font)              scaled to size 10.0pt on input line 23.
LaTeX Font Info:    Font shape `TU/SourceSansPro(2)/m/sl' in size <9> not avail
able
(Font)              Font shape `TU/SourceSansPro(2)/m/it' tried instead on inpu
t line 23.
LaTeX Font Info:    Font shape `TU/SourceSansPro(2)/m/it' will be
(Font)              scaled to size 9.0pt on input line 23.
LaTeX Font Info:    Font shape `TU/SourceSansPro(1)/m/n' will be
(Font)              scaled to size 9.0pt on input line 23.
LaTeX Font Info:    Font shape `TU/SourceSansPro(2)/m/n' will be
(Font)              scaled to size 8.0pt on input line 23.
LaTeX Font Info:    Font shape `TU/SourceSansPro(2)/m/sl' in size <8> not avail
able
(Font)              Font shape `TU/SourceSansPro(2)/m/it' tried instead on inpu
t line 23.
LaTeX Font Info:    Font shape `TU/SourceSansPro(2)/m/it' will be
(Font)              scaled to size 8.0pt on input line 23.
) (./cv/experience.tex
LaTeX Font Info:    Font shape `TU/SourceSansPro(2)/b/n' will be
(Font)              scaled to size 9.0pt on input line 25.
)
(./cv/projects.tex) (./cv/skills.tex
Overfull \hbox (26.50792pt too wide) in alignment at lines 10--25
[] []
 []

) (./cv/interests.tex

LaTeX Font Info:    Font shape `TU/SourceSansPro(0)/m/n' will be
(Font)              scaled to size 16.0pt on input line 6.
LaTeX Font Info:    Font shape `TU/SourceSansPro(1)/m/n' will be
(Font)              scaled to size 8.0pt on input line 6.
LaTeX Font Info:    Font shape `TU/SourceSansPro(1)/m/sc' will be
(Font)              scaled to size 8.0pt on input line 6.
[1

{C:/Users/<USER>/AppData/Local/MiKTeX/fonts/map/pdftex/pdftex.map}]
Overfull \hbox (23.25793pt too wide) in alignment at lines 12--27
[] []
 []

) (./cv/languages.tex
Overfull \hbox (16.0879pt too wide) in alignment at lines 9--21
[] []
 []

)

[2] (./resume.aux)
 ***********
LaTeX2e <2025-06-01> patch level 1
L3 programming layer <2025-07-20>
 ***********
Package logreq Info: Writing requests to 'resume.run.xml'.

\openout1 = resume.run.xml
)

Here is how much of LuaTeX's memory you used:
 42931 strings out of 470118
 156364,1977958 words of node,token memory allocated
 696 words of node memory still in use:
   6 hlist, 2 vlist, 2 rule, 2 glue, 4 kern, 1 glyph, 7 attribute, 93 glue_spec,
 7 attribute_list, 2 write nodes
   avail lists: 1:4,2:1791,3:726,4:597,5:2054,6:280,7:15056,8:1,9:1549,10:21,11:
938
 70479 multiletter control sequences out of 65536+600000
 113 fonts using 30901671 bytes
 131i,12n,127p,579b,1110s stack positions out of 10000i,1000n,20000p,200000b,200000s
<c:/users/<USER>/appdata/local/programs/miktex/fonts/opentype/adobe/sourcesanspro
/sourcesanspro-semibold.otf><c:/users/<USER>/appdata/local/programs/miktex/fonts/
opentype/adobe/sourcesanspro/sourcesanspro-lightit.otf><c:/users/<USER>/appdata/l
ocal/programs/miktex/fonts/opentype/adobe/sourcesanspro/sourcesanspro-light.otf>
<c:/users/<USER>/appdata/local/programs/miktex/fonts/opentype/adobe/sourcesanspro
/sourcesanspro-bold.otf><c:/users/<USER>/appdata/local/programs/miktex/fonts/open
type/public/fontawesome5/fontawesome5brands-regular-400.otf><c:/users/<USER>/appd
ata/local/programs/miktex/fonts/opentype/public/fontawesome5/fontawesome5free-so
lid-900.otf><c:/users/<USER>/appdata/local/programs/miktex/fonts/opentype/google/
roboto/roboto-italic.otf><c:/users/<USER>/appdata/local/programs/miktex/fonts/ope
ntype/adobe/sourcesanspro/sourcesanspro-regular.otf><c:/users/<USER>/appdata/loca
l/programs/miktex/fonts/opentype/google/roboto/roboto-regular.otf>
Output written on resume.pdf (2 pages, 47819 bytes).

PDF statistics: 92 PDF objects out of 1000 (max. 8388607)
 10 named destinations out of 1000 (max. 131072)
 16 words of extra memory for PDF output out of 10000 (max. 100000000)

